import asyncio
from functools import partial
from typing import Any, Callable, Coroutine, Dict, Optional

from langchain_core.messages import AIMessage, BaseMessage

from baml_client import b
from baml_client.types import (
    FlightPlanningStep,
    FlightPlanResponse,
    FlightType,
    HotelPlanningStep,
    HotelPlanResponse,
    HotelSearchAdditionalCriteria,
    HotelSearchCoreCriteria,
    HotelSegment,
    HotelSelectResult,
)
from flight_agent.flight_booking_util import FlightBookingUtil
from flight_agent.flights_helper import FlightBamlHelper
from front_of_house_agent.adapter import map_websocket_message
from front_of_house_agent.back_of_house_executor.executor import AgentExecutor
from front_of_house_agent.back_of_house_executor.flight_executor_functions import (
    flight_baggage_check,
    flight_booking_v2,
    flight_checkout,
    flight_credits_check,
    flight_validation,
)
from front_of_house_agent.back_of_house_executor.flight_search_functions import search_flights
from front_of_house_agent.back_of_house_executor.hotel_executor_functions import (
    hotel_booking,
    is_hotel_ready_to_book,
    search_hotels,
    validate_hotel,
)
from front_of_house_agent.back_of_house_executor.seat_selection_executor import check_available_seats
from front_of_house_agent.common_models import (
    FlightSearchSource,
    HotelValidationResult,
    TravelContext,
)
from front_of_house_agent.serp_flight_helper import SerpFlightSearchHelper
from hotel_agent.booking_dot_com_tools import BookingTools
from hotel_agent.hotels_helper import HotelsHelper
from llm_utils.llm_utils import get_flight_detail_from_history, parse_min_max_price
from server.database.models.chat_thread import ChatThread
from server.database.models.user_profile import UserProfile
from server.schemas.authenticate.user import User
from server.services.google_maps_api.get_lat_long import get_lat_long_from_loc_str
from server.services.trips.bookings import get_bookings_of_status
from server.services.user.user_activity import UserActivityType, update_user_activity
from server.services.user.user_preferences import (
    SearchType,
    increment_triggered_search_amount,
)
from server.utils.analytics.analytics import TrackingEvent, TrackingManager
from server.utils.logger import logger
from server.utils.message_constants import SEARCH_UPDATE_MESSAGES
from server.utils.mongo_connector import trip_context_v2_collection
from server.utils.settings import settings
from virtual_travel_agent.helpers import console_masks, get_current_date_string

flight_mask = console_masks["flight"]
hotel_mask = console_masks["hotel"]
other_mask = console_masks["other"]


class TripPlanExecutor(AgentExecutor):
    def __init__(
        self,
        hotel_helper: HotelsHelper,
        flight_helper: FlightBamlHelper,
        message_sender: partial[Coroutine[Any, Any, None]],
        thread: ChatThread,
        timezone: str | None,
        user: User,
        user_profile: UserProfile | None,
    ):
        super().__init__(message_sender)
        self.hotel_helper = hotel_helper
        self.flight_helper = flight_helper
        self.message_sender = message_sender
        self.thread = thread
        self.timezone = timezone
        self.user = user
        self.booking_dot_com_tools = BookingTools(user=user)
        self.flight_booking_util = FlightBookingUtil(thread=thread, user_profile=user_profile, user=user)

        self.serp_flight_search = SerpFlightSearchHelper(user=user, thread=thread, timezone=timezone)

        self.flight_choice_from_outbound_search: dict[str, Any] | None = None

    async def execute_flight_plan(
        self,
        trip_plan_state: FlightPlanResponse,
        travel_context: TravelContext,
        message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
        extral_params: Dict[str, Any],
        callback_handlers: Dict[str, Callable],
    ):
        """Execute flight plan steps."""
        step = trip_plan_state.current_step
        step_name = step.name if isinstance(step, FlightPlanningStep) else step
        task_id = f"{self.thread.id}_{step_name}"
        if step_name == FlightPlanningStep.FLIGHT_BOOKING.name:
            for previous_step in [
                FlightPlanningStep.FLIGHT_CHECKOUT.name,
                FlightPlanningStep.FLIGHT_VALIDATION.name,
                FlightPlanningStep.FLIGHT_CREDIT_CHECK.name,
            ]:
                previous_task_id = f"{self.thread.id}_{previous_step}"
                if previous_task_id in self.task_map and not self.task_map[previous_task_id].done():
                    logger.info(
                        "final booking step's previous task is not done yet, can't do flight booking, so not start flight booking",
                    )
                    return

        if self.task_map.get(task_id) and not self.task_map[task_id].done():
            if step_name == FlightPlanningStep.FLIGHT_BOOKING.name:
                self.schedule_send_message(
                    message={
                        "type": "search_update",
                        "text": SEARCH_UPDATE_MESSAGES["FLIGHT_BOOKING_WAIT"],
                        "isBotMessage": True,
                        "expectResponse": False,
                    },
                )
                return None

            logger.info(f"Cancelling task: {task_id}", other_mask)
            self.task_map[task_id].cancel()

        task = None

        # Handle flight search steps (outbound, flight_search, and return)
        if step_name in [
            FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH.name,
            FlightPlanningStep.MULTI_LEG_FLIGHT_SEARCH.name,
            FlightPlanningStep.RETURN_FLIGHT_SEARCH.name,
        ]:
            # Cancel previous task and create new one
            self.cancel_task(task_id)

            task = self.wrap_task(
                asyncio.create_task(
                    search_flights(
                        self,
                        step_name,  # type: ignore
                        trip_plan_state.current_searching_segment_index,
                        travel_context,
                        message_persistor,
                        extral_params,
                        callback_handlers,
                    ),
                    name=task_id,
                )
            )

            # Handle step-specific additional tasks
            if step_name in [
                FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH.name,
                FlightPlanningStep.MULTI_LEG_FLIGHT_SEARCH.name,
            ]:
                asyncio.create_task(
                    self.record_search_trigger(SearchType.FLIGHT),
                    name=f"{task_id}_record_search_trigger",
                )
            elif step_name == FlightPlanningStep.RETURN_FLIGHT_SEARCH.name:
                await TrackingManager.log_event_in_background(
                    event_type=TrackingEvent.FLIGHT_SEARCHED,
                    user_id=str(self.user.id),
                    user_email=self.user.email,
                    event_properties={
                        "origin": travel_context.flight_search_core_criteria.departure_airport_code,
                        "destination": travel_context.flight_search_core_criteria.arrival_airport_code,
                        "departure_date": travel_context.flight_search_core_criteria.outbound_date,
                    },
                )

        elif step_name == FlightPlanningStep.FLIGHT_BAGGAGE_CHECK.name:
            # Get spotnana flight ID
            spotnana_flight_id = (
                travel_context.flight_select_result.matched_flight_id
                or travel_context.flight_select_result.selected_return_flight_id
                or travel_context.flight_select_result.selected_outbound_flight_id
            )
            if travel_context.flight_search_core_criteria.flight_type == FlightType.MultiLegs:
                spotnana_flight_id = (
                    travel_context.flight_select_result.selected_flight_for_segment[-1].selected_flight_id
                    if travel_context.flight_select_result.selected_flight_for_segment
                    else None
                )

            if travel_context.selected_outbound_flight:
                task = self.wrap_task(
                    asyncio.create_task(
                        flight_baggage_check(
                            self,
                            travel_context.selected_outbound_flight,
                            travel_context.selected_return_flight,
                            travel_context.selected_flight_for_segment,
                            travel_context.flight_select_result,
                            message_persistor,
                            callback_handlers.get(step_name),
                            travel_context.flight_select_result.search_source,
                            travel_context.flight_select_result.search_id,
                            spotnana_flight_id,
                        ),
                        name=task_id,
                    )
                )

        elif step_name == FlightPlanningStep.FLIGHT_CHECKOUT.name:
            # Get spotnana flight ID
            spotnana_flight_id = (
                travel_context.flight_select_result.selected_return_flight_id
                or travel_context.flight_select_result.selected_outbound_flight_id
            )
            spotnana_search_id = travel_context.flight_select_result.search_id
            if travel_context.flight_search_core_criteria.flight_type == FlightType.MultiLegs:
                spotnana_flight_id = (
                    travel_context.flight_select_result.selected_flight_for_segment[-1].selected_flight_id
                    if travel_context.flight_select_result.selected_flight_for_segment
                    else None
                )
                if spotnana_flight_id:
                    candidate = get_flight_detail_from_history(extral_params.get("messages") or [], spotnana_flight_id)
                    if candidate:
                        spotnana_search_id = candidate[1]

            task = self.wrap_task(
                asyncio.create_task(
                    flight_checkout(
                        self,
                        callback_handlers.get(step_name),
                        message_persistor,
                        spotnana_search_id,
                        spotnana_flight_id,
                        travel_context,
                        extral_params,
                        callback_handlers,
                    ),
                    name=task_id,
                )
            )

        elif step_name == FlightPlanningStep.FLIGHT_VALIDATION.name:
            # Get spotnana flight ID
            spotnana_search_id = travel_context.flight_select_result.search_id
            spotnana_flight_id = (
                travel_context.flight_select_result.matched_flight_id
                or travel_context.flight_select_result.selected_return_flight_id
                or travel_context.flight_select_result.selected_outbound_flight_id
            )
            if travel_context.flight_search_core_criteria.flight_type == FlightType.MultiLegs:
                spotnana_flight_id = (
                    travel_context.flight_select_result.selected_flight_for_segment[-1].selected_flight_id
                    if travel_context.flight_select_result.selected_flight_for_segment
                    else None
                )
                if spotnana_flight_id:
                    candidate = get_flight_detail_from_history(extral_params.get("messages") or [], spotnana_flight_id)
                    if candidate:
                        spotnana_search_id = candidate[1]

            if (
                travel_context.flight_select_result.search_source is None
                or travel_context.flight_select_result.search_source == FlightSearchSource.SERP
            ) and not travel_context.flight_select_result.matched_flight_id:
                logger.warning(
                    "Flight validation step has matched_flight_id, let's go back to checkout",
                    other_mask,
                )

                assert (
                    travel_context.selected_outbound_flight is not None
                ), "in flight validation step, selected outbound flight should not be None"
                task = self.wrap_task(
                    asyncio.create_task(
                        flight_checkout(
                            self,
                            callback_handlers.get(FlightPlanningStep.FLIGHT_CHECKOUT.name),
                            message_persistor,
                            spotnana_search_id,
                            spotnana_flight_id,
                            travel_context,
                            extral_params,
                            callback_handlers,
                        ),
                        name=task_id,
                    )
                )
            else:
                task = self.wrap_task(
                    asyncio.create_task(
                        flight_validation(
                            self,
                            travel_context.selected_outbound_flight,
                            travel_context.selected_return_flight,
                            travel_context.selected_flight_for_segment,
                            travel_context.flight_select_result,
                            travel_context.flight_seat_selection or [],
                            travel_context.flight_select_result.search_source,
                            spotnana_search_id,
                            spotnana_flight_id,
                            message_persistor,
                        ),
                        name=task_id,
                    )
                )

        elif step_name == FlightPlanningStep.CHECK_SEAT_AVAILABILITY.name:
            # Get spotnana flight ID
            spotnana_search_id = travel_context.flight_select_result.search_id
            spotnana_flight_id = (
                travel_context.flight_select_result.matched_flight_id
                or travel_context.flight_select_result.selected_return_flight_id
                or travel_context.flight_select_result.selected_outbound_flight_id
            )
            if travel_context.flight_search_core_criteria.flight_type == FlightType.MultiLegs:
                spotnana_flight_id = (
                    travel_context.flight_select_result.selected_flight_for_segment[-1].selected_flight_id
                    if travel_context.flight_select_result.selected_flight_for_segment
                    else None
                )
                if spotnana_flight_id:
                    candidate = get_flight_detail_from_history(extral_params.get("messages") or [], spotnana_flight_id)
                    if candidate:
                        spotnana_search_id = candidate[1]

            assert isinstance(trip_plan_state, FlightPlanResponse)
            task = self.wrap_task(
                asyncio.create_task(
                    check_available_seats(
                        self,
                        spotnana_search_id,
                        spotnana_flight_id,
                        extral_params.get("preferred_seat_types") or [],
                        self.user.email,
                        extral_params.get("message_strs") or [],
                        trip_plan_state.availability_param,
                        message_persistor,
                    ),
                    name=task_id,
                )
            )

        elif step_name == FlightPlanningStep.FLIGHT_CREDIT_CHECK.name:
            flight_options = []
            if travel_context.selected_flight_for_segment:
                flight_options = list(travel_context.selected_flight_for_segment.values())
            else:
                if travel_context.selected_outbound_flight:
                    flight_options.append(travel_context.selected_outbound_flight)
                if travel_context.selected_return_flight:
                    flight_options.append(travel_context.selected_return_flight)

            task = self.wrap_task(
                asyncio.create_task(
                    flight_credits_check(
                        self,
                        flight_options,
                        message_persistor,
                    ),
                    name=task_id,
                )
            )

        elif step_name == FlightPlanningStep.FLIGHT_BOOKING.name:
            # Get spotnana flight ID
            spotnana_search_id = travel_context.flight_select_result.search_id
            spotnana_flight_id = (
                travel_context.flight_select_result.selected_return_flight_id
                or travel_context.flight_select_result.selected_outbound_flight_id
            )
            if travel_context.flight_search_core_criteria.flight_type == FlightType.MultiLegs:
                spotnana_flight_id = (
                    travel_context.flight_select_result.selected_flight_for_segment[-1].selected_flight_id
                    if travel_context.flight_select_result.selected_flight_for_segment
                    else None
                )
                if spotnana_flight_id:
                    candidate = get_flight_detail_from_history(extral_params.get("messages") or [], spotnana_flight_id)
                    if candidate:
                        spotnana_search_id = candidate[1]

            task = self.wrap_task(
                asyncio.create_task(
                    flight_booking_v2(
                        self,
                        callback_handlers.get(FlightPlanningStep.FLIGHT_VALIDATION.name),
                        travel_context,
                        message_persistor,
                        callback_handlers.get(step_name),
                        spotnana_search_id,
                        spotnana_flight_id,
                        extral_params,
                        callback_handlers,
                    ),
                    name=task_id,
                )
            )

            selected_flight = travel_context.selected_return_flight or travel_context.selected_outbound_flight
            ticket_price_usd = selected_flight.total_price if selected_flight else None

            await TrackingManager.log_event_in_background(
                event_type=TrackingEvent.FLIGHT_BOOKED,
                user_id=str(self.user.id),
                user_email=self.user.email,
                event_properties={
                    "airline": selected_flight.airline_code if selected_flight else None,
                    "ticket_price_usd": ticket_price_usd,
                },
            )

        return task

    async def execute_hotel_plan(
        self,
        trip_plan_state: HotelPlanResponse,
        hotel_segments: list[HotelSegment] | None,
        hotel_search_criteria_core: HotelSearchCoreCriteria,
        hotel_search_criteria_additional: HotelSearchAdditionalCriteria,
        hotel_validation_result: HotelValidationResult,
        hotel_validation_result_for_segment: Optional[Dict[str, HotelValidationResult]],
        hotel_select_result: HotelSelectResult,
        selected_hotel_for_segment: Optional[Dict[str, HotelSelectResult]],
        selected_hotel_option_for_segment: Optional[Dict[str, Dict[str, Any]]],
        message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
        extral_params: Dict[str, Any],
        on_hotel_validated: Callable[
            [str | None, float | None, str | None, dict[str, HotelValidationResult] | None], Coroutine[Any, None, None]
        ]
        | None,
        on_hotel_booked: Callable[[], Coroutine[Any, None, None]] | None,
        is_mobile: bool = False,
    ):
        """Execute hotel plan steps."""
        step = trip_plan_state.current_step
        step_name = step.name if isinstance(step, HotelPlanningStep) else step
        task_id = f"{self.thread.id}_{step_name}"

        if self.task_map.get(task_id) and not self.task_map[task_id].done():
            if (
                step_name == HotelPlanningStep.HOTEL_SEARCH.name
                and trip_plan_state.updated_hotel_search_core_criteria is None
                and trip_plan_state.updated_hotel_search_additional_criteria is None
            ):
                return None

            if step_name == HotelPlanningStep.HOTEL_BOOKING.name:
                self.schedule_send_message(
                    message={
                        "type": "search_update",
                        "text": SEARCH_UPDATE_MESSAGES["HOTEL_BOOKING_WAIT"],
                        "isBotMessage": True,
                        "expectResponse": False,
                    },
                )
                return None

            logger.info(f"Cancelling task: {task_id}", other_mask)
            self.task_map[task_id].cancel()

        task = None
        if step_name == HotelPlanningStep.HOTEL_SEARCH.name:
            min_price, max_price = parse_min_max_price(hotel_search_criteria_additional.price_range)
            hotel_search_dict = {
                "check_in_date": hotel_search_criteria_core.check_in_date,
                "check_out_date": hotel_search_criteria_core.check_out_date,
                "hotel_search_radius": hotel_search_criteria_core.hotel_search_radius,
                "travel_context": (hotel_search_criteria_core.model_dump_json(exclude_none=True) or "")
                + ", "
                + (hotel_search_criteria_additional.model_dump_json(exclude_none=True) or ""),
                "messages": extral_params.get("message_strs") or [],
                "preferred_payment_timings": hotel_search_criteria_additional.preferred_payment_timings,
                "min_price": min_price,
                "max_price": max_price,
                "is_mobile": is_mobile,
            }
            if hotel_search_criteria_additional.brand_hotel_preferences:
                hotel_search_dict["brand_hotel_preferences"] = hotel_search_criteria_additional.brand_hotel_preferences

            location_name = None
            if hotel_search_criteria_additional.location_neighborhoods_districts_landmarks:
                location_name = hotel_search_criteria_additional.location_neighborhoods_districts_landmarks
            elif hotel_search_criteria_additional.street_name:
                location_name = hotel_search_criteria_additional.street_name

            if hotel_search_criteria_core.city:
                if location_name:
                    location_name = f"{location_name}, {hotel_search_criteria_core.city}"
                else:
                    location_name = f"Downtown area of {hotel_search_criteria_core.city} the city"

            lat_long_str = None
            if location_name:
                lat_long_str = await get_lat_long_from_loc_str(location_name)
                logger.info(
                    f"Location lat long of {location_name}:\n"
                    f"from Google Maps API: {lat_long_str}\n"
                    f"from LLM: {hotel_search_criteria_core.location_latitude_longitude}",
                    hotel_mask,
                )
                if lat_long_str:
                    hotel_search_criteria_core.location_latitude_longitude = lat_long_str
                    hotel_search_dict["location_latitude_longitude"] = lat_long_str

            if lat_long_str is None:
                logger.warning(
                    f"Failed to get lat long for {location_name}, using LLM lat long: {hotel_search_criteria_core.location_latitude_longitude}",
                    hotel_mask,
                )
                hotel_search_dict["location_latitude_longitude"] = (
                    hotel_search_criteria_core.location_latitude_longitude
                )

            task = self.wrap_task(
                asyncio.create_task(
                    search_hotels(
                        self,
                        hotel_select_result,
                        hotel_search_dict,
                        extral_params.get("messages") or [],
                        message_persistor,
                        self.timezone,
                    ),
                    name=task_id,
                )
            )

            await TrackingManager.log_event_in_background(
                event_type=TrackingEvent.HOTEL_SEARCHED,
                user_id=str(self.user.id),
                user_email=self.user.email,
                event_properties={
                    "city": hotel_search_criteria_core.city,
                    "check_in": hotel_search_criteria_core.check_in_date,
                    "check_out": hotel_search_criteria_core.check_out_date,
                    "location_name": location_name,
                },
            )

            asyncio.create_task(
                self.record_search_trigger(SearchType.HOTEL),
                name=f"{task_id}_record_search_trigger",
            )

            await trip_context_v2_collection.update_one(
                {"thread_id": self.thread.id},
                {
                    "$set": {
                        "hotel_search_core_criteria": hotel_search_criteria_core.model_dump(),
                    }
                },
                upsert=True,
            )

        elif step_name == HotelPlanningStep.HOTEL_VALIDATION.name:
            task = self.wrap_task(
                asyncio.create_task(
                    validate_hotel(
                        self,
                        hotel_select_result,
                        hotel_search_criteria_core,
                        message_persistor,
                        on_hotel_validated,
                        hotel_segments,
                        selected_hotel_for_segment,
                        extral_params.get("message_strs") or [],
                    ),
                    name=task_id,
                )
            )

        elif step_name == HotelPlanningStep.HOTEL_BOOKING.name:
            if await is_hotel_ready_to_book(
                hotel_segments,
                hotel_select_result,
                hotel_validation_result,
                hotel_validation_result_for_segment,
                selected_hotel_for_segment,
            ):
                task = self.wrap_task(
                    asyncio.create_task(
                        hotel_booking(
                            self,
                            hotel_search_criteria_core.check_in_date or "",
                            hotel_search_criteria_core.check_out_date or "",
                            hotel_search_criteria_core.city,
                            hotel_select_result,
                            hotel_validation_result,
                            message_persistor,
                            extral_params.get("messages") or [],
                            on_hotel_booked,
                            hotel_segments,
                            hotel_validation_result_for_segment,
                            selected_hotel_for_segment,
                            selected_hotel_option_for_segment,
                        ),
                        name=task_id,
                    )
                )
                # Track hotel booking event
                await TrackingManager.log_event_in_background(
                    event_type=TrackingEvent.HOTEL_BOOKED,
                    user_id=str(self.user.id),
                    user_email=self.user.email,
                    event_properties={
                        "total_price_usd": hotel_validation_result.validated_price,
                        "hotel_name": hotel_select_result.hotel_name,
                    },
                )

            else:
                self.schedule_send_message(
                    message={
                        "type": "search_update",
                        "text": SEARCH_UPDATE_MESSAGES["HOTEL_VALIDATION_RETRY"],
                        "isBotMessage": True,
                        "expectResponse": False,
                    }
                )

                task = self.wrap_task(
                    asyncio.create_task(
                        validate_hotel(
                            self,
                            hotel_select_result,
                            hotel_search_criteria_core,
                            message_persistor,
                            on_hotel_validated,
                            hotel_segments,
                            selected_hotel_for_segment,
                            extral_params.get("message_strs") or [],
                        ),
                        name=task_id,
                    )
                )

        if task:
            logger.info(f"Created task: {task_id}", other_mask)
            return task
        return None

    def schedule_send_message(self, message: dict[str, Any]):
        asyncio.create_task(self.message_sender(message=message))

    async def send_post_booking_message(
        self,
        message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
    ):
        # limit to 2 bookings because for now users can only have 2 booked bookings one for flight and one for hotel
        bookings = await get_bookings_of_status(self.thread, ["booked", "pending"], limit=10)
        flight_booked = False
        hotel_booked = False
        if bookings:
            for booking in bookings:
                if booking.type == "flight":
                    flight_booked = True
                elif booking.type == "accommodations":
                    hotel_booked = True

        logger.info(
            f"Sending post booking message with flight_booked: {flight_booked}, hotel_booked: {hotel_booked}",
            flight_mask,
        )
        response = await b.ConversePostBookingLight(
            flight_booked=flight_booked,
            hotel_booked=hotel_booked,
            current_date=get_current_date_string(self.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )
        logger.log_baml()
        new_message = AIMessage(content=response)

        await message_persistor([new_message])
        message = await map_websocket_message(
            new_message,
            None,
            None,
        )
        await self.message_sender(message={**message[0]})

    async def record_search_trigger(self, search_type: SearchType):
        updated_preferences = {}
        activity_type = None

        if search_type == SearchType.FLIGHT:
            updated_preferences = await increment_triggered_search_amount(self.user.id, search_type=SearchType.FLIGHT)
            activity_type = UserActivityType.FLIGHT_SEARCH
        elif search_type == SearchType.HOTEL:
            updated_preferences = await increment_triggered_search_amount(self.user.id, search_type=SearchType.HOTEL)
            activity_type = UserActivityType.HOTEL_SEARCH

        if activity_type:
            asyncio.create_task(
                update_user_activity(str(self.user.id), activity_type),
                name=f"update_user_activity_{activity_type}",
            )

        if updated_preferences:
            total_triggered_search_amount = (
                updated_preferences.triggered_flight_search_amount + updated_preferences.triggered_hotel_search_amount
            )
            if total_triggered_search_amount == settings.HIDE_SAMPLES_AFTER_N_SEARCHES:
                await self.message_sender(
                    message={
                        "type": "profile_update",
                        "expectResponse": True,
                    }
                )
