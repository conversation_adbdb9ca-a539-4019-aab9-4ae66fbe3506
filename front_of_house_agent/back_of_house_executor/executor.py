import asyncio
import traceback
from functools import partial
from typing import Any, Callable, Coroutine

from server.services.task_manager.task_manager import GlobalTaskManager
from server.utils.logger import logger
from server.utils.settings import AgentTypes, settings


class AgentExecutor:
    def __init__(self, message_sender: partial[Coroutine[Any, Any, None]]):
        self.task_map: dict[str, asyncio.Task] = {}
        self.message_sender = message_sender
        self.step_callbacks: list[Callable[[str], Coroutine[Any, Any, None]]] = []

    def add_step_callback(self, callback: Callable[[str], Coroutine[Any, Any, None]]):
        """Add a callback to be executed after each step completes."""
        self.step_callbacks.append(callback)

    def stop_all_task(self):
        for task in self.task_map.values():
            if not task.done():
                task.cancel()

    def cancel_task(self, task_name: str) -> bool:
        task = self.task_map.get(task_name)
        if task is None:
            logger.warning(f"Task: '{task_name}' not found in task map")
            return False

        if task.done():
            logger.info(f"Task: '{task_name}' is already completed")
            return False

        task.cancel()
        logger.info(f"Task: '{task_name}' has been cancelled")
        return True

    def wrap_task(self, task: asyncio.Task):
        def __handle_task_done(task: asyncio.Task):
            task_name = task.get_name()
            self.task_map.pop(task_name, None)

            if task.cancelled():
                asyncio.create_task(
                    self.message_sender(
                        message={
                            "type": "prompt",
                            "isStopped": True,
                            "expectResponse": True,
                            "isBotMessage": True,
                            "text": "",
                        }
                    )
                )
                return

            if exc := task.exception():
                logger.error(f"Error running task: {task.exception()}")
                logger.error(f"Error details: {traceback.format_exception(type(exc), exc, exc.__traceback__)}")

                ask_to_retry_msg = "Apologies, something didn't go as planned. Could you try asking again with a bit more detail or perhaps in a different way?"
                asyncio.create_task(
                    self.message_sender(
                        message={
                            "type": "prompt",
                            "status": "error",
                            "textColor": settings.AGENT_MESSAGE_COLOR_MAP[AgentTypes.ERROR],
                            "reason": (
                                "\n".join(
                                    traceback.format_exception(type(exc), exc, exc.__traceback__) + [ask_to_retry_msg]
                                )
                                if settings.OTTO_ENV.upper() in ("DEV", "STG")
                                else ask_to_retry_msg
                            ),
                        }
                    )
                )
                return

            # Execute step callbacks after successful completion
            for callback in self.step_callbacks:
                try:
                    asyncio.create_task(callback("_".join(task_name.split("_")[1:])))
                except Exception as e:
                    logger.error(f"Error executing step callback for {task_name}: {e}")

        task.add_done_callback(__handle_task_done)
        self.task_map[task.get_name()] = task
        GlobalTaskManager.register_task(task)

        return task
