import asyncio
from enum import Enum
from functools import partial
from typing import Any, Coroutine

from langchain_core.messages import AIMessage, BaseMessage

from baml_client import b
from baml_client.types import (
    CancelFlightStep,
    CancelHotelStep,
    CancellationPlan,
)
from flight_agent.flights_helper import FlightBamlHelper
from front_of_house_agent.adapter import map_websocket_message
from front_of_house_agent.back_of_house_executor.executor import Agent<PERSON><PERSON><PERSON><PERSON>
from front_of_house_agent.cancellation_agent.cancel_flight_executor import FlightCancelExecutor
from front_of_house_agent.cancellation_agent.cancel_hotel_executor import HotelCancelExecutor
from front_of_house_agent.common_models import TravelContext
from hotel_agent.hotels_helper import HotelsHelper
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.database.models.chat_thread import ChatThread
from server.schemas.authenticate.user import User
from server.services.memory.memory_store import MemoryStore
from server.services.memory.trips.memory_modules.bookings_memory import BookingsMemory
from server.services.trips.bookings import (
    get_bookings_of_status,
)
from server.services.user.user_activity import UserActivityType, update_user_activity
from server.utils.logger import logger
from server.utils.settings import AgentTypes, settings
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory


class CancellationStep(Enum):
    Validation = "Validation"
    Confirmed = "Confirmed"
    DONE = "DONE"

    @staticmethod
    def from_flight(step: CancelFlightStep | None) -> "CancellationStep":
        match step:
            case CancelFlightStep.VERIFY_TICKET_CANCELLABLE:
                return CancellationStep.Validation
            case CancelFlightStep.CANCEL_TICKET:
                return CancellationStep.Confirmed
            case CancelFlightStep.NONE:
                return CancellationStep.DONE
            case _:
                logger.warn(f"Unknown flight cancellation step: {step}")
                return CancellationStep.DONE

    @staticmethod
    def from_hotel(step: CancelHotelStep | None) -> "CancellationStep":
        match step:
            case CancelHotelStep.CHECK_CANCELLABILITY:
                return CancellationStep.Validation
            case CancelHotelStep.SUBMIT_CANCELLATION:
                return CancellationStep.Confirmed
            case CancelHotelStep.NONE:
                return CancellationStep.DONE
            case _:
                logger.warn(f"Unknown flight cancellation step: {step}")
                return CancellationStep.DONE


class CancellationAgent:
    def __init__(
        self,
        user: User,
        travel_context: TravelContext,
        messages: list[BaseMessage],
        history: PostgresChatMessageHistory,
        thread: ChatThread,
        websocket_send_message: partial[Coroutine[Any, Any, None]],
        timezone: str | None = None,
        email_mode: bool = False,
    ):
        self.user = user
        self.travel_context = travel_context
        self.messages = messages
        self.history = history
        self.thread = thread
        self.websocket_send_message = websocket_send_message
        self.timezone = timezone
        self.email_mode = email_mode
        self.booking_mem = BookingsMemory(user_id=str(self.user.id), thread_id=str(self.thread.id))

        self.cancel_flight_executor = FlightCancelExecutor(
            flight_helper=FlightBamlHelper(user=user, timezone=timezone, thread=thread, existing_user_preferences=None),
            message_sender=websocket_send_message,
            thread=thread,
        )

        self.cancel_hotel_executor = HotelCancelExecutor(
            hotel_helper=HotelsHelper(user=user, timezone=timezone, thread_id=thread.id),
            message_sender=websocket_send_message,
            thread=thread,
        )

        self.executors: list[AgentExecutor] = [self.cancel_flight_executor, self.cancel_hotel_executor]

    async def handle_cancellation_plan(self):
        total_messages = []
        retry_count = 3
        while True:
            retry_count -= 1
            if retry_count < 0:
                logger.warn("Cancellation plan generation failed after 3 retries.")
                break

            bookings = []
            for booking in await get_bookings_of_status(self.thread, statuses=["booked", "pending", "cancelled"]):
                content = dict(booking.content)
                content.pop("photos", None)
                bookings.append({booking.type: content})
            logger.debug(f"bookings to cancel: {bookings}")

            plan = await b.PlanForCancellation(
                bookings=str(bookings),
                messages=get_message_buffer_as_strings(self.messages[-10:]),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                user_name=self.user.name,
                baml_options={"collector": logger.collector},
            )
            logger.log_baml()

            msg = AIMessage(
                plan.agent_response or "",
                additional_kwargs={
                    "function_call": {"arguments": plan.model_dump_json(exclude={"agent_response"})},
                    "agent_classification": AgentTypes.CANCELLATION,
                },
            )

            total_messages.append(msg)
            await self._persist_messages(msg)
            await self._send_socket_message(message=msg)

            if plan.finished or not plan.tasks:
                break

            msg, step = await self.handle_cancellation_tasks(plan)
            if msg:
                total_messages.append(msg)
                await self._persist_messages(msg)
                await self._send_socket_message(message=msg)
            if step == CancellationStep.Validation or step == CancellationStep.DONE:
                break

        if self.email_mode:
            return {"messages": total_messages}
        else:
            return {"messages": []}

    async def handle_cancellation_tasks(self, plan: CancellationPlan):
        if not plan or not plan.tasks or len(plan.tasks) < 1 or plan.index is None or plan.index >= len(plan.tasks):
            return None, None

        task = plan.tasks[plan.index]
        if not task:
            return None, None

        step = CancellationStep.DONE
        message = None
        if state := task.flight_state:  # cancel flight
            step = CancellationStep.from_flight(state.current_step)

            message = await self.cancel_flight_executor.execute(state, [])
            asyncio.create_task(
                update_user_activity(
                    str(self.user.id),
                    UserActivityType.FLIGHT_CANCEL_ATTEMPTED,
                )
            )

        elif state := task.hotel_state:  # cancel hotel
            step = CancellationStep.from_hotel(state.current_step)

            message = await self.cancel_hotel_executor.execute(state)
            asyncio.create_task(
                update_user_activity(
                    str(self.user.id),
                    UserActivityType.HOTEL_CANCEL_ATTEMPTED,
                )
            )

        return message, step

    async def _persist_messages(self, message: BaseMessage):
        self.messages.append(message)
        self.history.add_pending_message(message)
        await self.history.apersist()

    async def _send_socket_message(self, message: BaseMessage):
        socket_messages = await map_websocket_message(message)
        if len(socket_messages):
            await self.websocket_send_message(message=socket_messages[0])

    def _search_booking_mem(self, query: str):
        mem = MemoryStore().memory
        flight_mem = mem.search(query, user_id=self.user.id, filters={"type": "FLIGHT_BOOKING"}, limit=10)
        hotel_mem = mem.search(query, user_id=self.user.id, filters={"type": "ACCOMMODATION_BOOKING"}, limit=10)

        return sorted(flight_mem.get("results", []) + hotel_mem.get("results", []), key=lambda x: x["score"])
