import json
from typing import TYPE_CHECKING, Any, Callable, Optional

from langchain_core.messages import BaseMessage

from baml_client import b
from baml_client.types import (
    HotelSearchCoreCriteria,
    ResponseAllPreferences,
)
from front_of_house_agent.common_models import TravelContext
from front_of_house_agent.front_of_house_agent import FrontOfHouseWorkAgentType
from front_of_house_agent.sub_agent.trip_planner_sub_agent import (
    handle_hotel_planner_v2,
)
from front_of_house_agent.worker_agents.agent import (
    AgentValidationResponse,
    ValidationIssue,
    ValidationIssueType,
    WorkerAgent,
)
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.utils.settings import AgentTypes
from virtual_travel_agent.helpers import (
    get_current_date_string,
)

if TYPE_CHECKING:
    import front_of_house_agent.front_of_house_agent as fha


class HotelAgentResponse(AgentValidationResponse):
    def __init__(
        self,
        hotel_search_core_criteria: HotelSearchCoreCriteria,
    ):
        self.hotel_search_core_criteria = hotel_search_core_criteria

    def get_agent_type(self) -> AgentTypes:
        return AgentTypes.HOTELS

    def extract_validation_issues(self) -> list[ValidationIssue]:
        validation_issues = []
        if not self.hotel_search_core_criteria.check_in_date:
            validation_issues.append(
                ValidationIssue(
                    type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                    name="check_in_date",
                    description="The check in date for the hotel",
                )
            )
        if not self.hotel_search_core_criteria.check_out_date:
            validation_issues.append(
                ValidationIssue(
                    type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                    name="check_out_date",
                    description="The check out date for the hotel",
                )
            )
        if not self.hotel_search_core_criteria.city:
            validation_issues.append(
                ValidationIssue(
                    type=ValidationIssueType.MISSING_REQUIRED_FIELD, name="city", description="The city for the hotel"
                )
            )

        if self.hotel_search_core_criteria.is_multi_city_trip:
            if not self.hotel_search_core_criteria.hotel_segments:
                validation_issues.append(
                    ValidationIssue(
                        type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                        name="hotel_segments",
                        description="The hotel segments for the multi-city trip",
                    )
                )

        if self.hotel_search_core_criteria.hotel_segments:
            for segment in self.hotel_search_core_criteria.hotel_segments:
                if not segment.city:
                    validation_issues.append(
                        ValidationIssue(
                            type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                            name="city",
                            description=f"The city for the {segment.segment_index} hotel segment",
                        )
                    )
                if not segment.check_in_date:
                    validation_issues.append(
                        ValidationIssue(
                            type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                            name="check_in_date",
                            description=f"The check in date for the {segment.segment_index} hotel segment",
                        )
                    )
                if not segment.check_out_date:
                    validation_issues.append(
                        ValidationIssue(
                            type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                            name="check_out_date",
                            description=f"The check out date for the {segment.segment_index} hotel segment",
                        )
                    )

        return validation_issues


class HotelAgent(WorkerAgent):
    def __init__(self, timezone: Optional[str]):
        self.timezone = timezone

    async def validate(
        self,
        current_work_type: FrontOfHouseWorkAgentType,
        travel_context: TravelContext,
        user_preferences: ResponseAllPreferences,
        messages: list[BaseMessage],
    ) -> AgentValidationResponse:
        message_buffer_strs = get_message_buffer_as_strings(messages)
        hotel_search_core_criteria = await b.HotelSearchCriteriaExtractor(
            travel_preference=user_preferences.model_dump_json(exclude_none=True),
            hotel_search_core_criteria=travel_context.hotel_search_core_criteria.model_dump_json(exclude_none=True),
            selected_flight_itinerary=json.dumps(travel_context.to_selected_flights()),
            messages=message_buffer_strs,
            current_date=get_current_date_string(self.timezone),
        )
        return HotelAgentResponse(hotel_search_core_criteria)

    async def execute_plan(
        self,
        foh: "fha.FrontOfHouseAgent",
        classified_type_from_foh: FrontOfHouseWorkAgentType,
        messages: list[BaseMessage],
        prompt_user_preferences: Callable | None,
        tool_call_id: str,
    ) -> dict[str, Any]:
        return await handle_hotel_planner_v2(
            foh, classified_type_from_foh, messages, prompt_user_preferences, tool_call_id
        )
