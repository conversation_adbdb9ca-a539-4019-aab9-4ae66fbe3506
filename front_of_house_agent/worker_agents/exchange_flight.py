from typing import TYPE_CHECKING, Any, Optional

from langchain_core.messages import BaseMessage

from baml_client import b
from baml_client.types import (
    ResponseAllPreferences,
)
from flight_agent.flights_helper import FlightBamlHelper

# Add imports for the check logic
from front_of_house_agent.common_models import TravelContext
from front_of_house_agent.front_of_house_agent import FrontOfHouseWorkAgentType
from front_of_house_agent.sub_agent.exchange_flight_sub_agent import (
    handle_exchange_flight_v2,
)
from front_of_house_agent.worker_agents.agent import (
    AgentValidationResponse,
    ValidationIssue,
    ValidationIssueType,
    WorkerAgent,
)
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.database.models.chat_thread import ChatThread
from server.utils.logger import logger
from server.utils.settings import AgentTypes
from virtual_travel_agent.helpers import (
    get_current_date_string,
)

if TYPE_CHECKING:
    import front_of_house_agent.front_of_house_agent as fha


class ExchangeFlightAgentResponse(AgentValidationResponse):
    def __init__(
        self,
        able_to_exchange: bool,
        why_not_exchangeable: str | None,
    ):
        self.able_to_exchange = able_to_exchange
        self.why_not_exchangeable = why_not_exchangeable

    def get_agent_type(self) -> AgentTypes:
        return AgentTypes.EXCHANGE_FLIGHTS

    def extract_validation_issues(self) -> list[ValidationIssue]:
        validation_issues = []

        # Check if operation is feasible
        if not self.able_to_exchange:
            validation_issues.append(
                ValidationIssue(
                    type=ValidationIssueType.OPERATION_NOT_FEASIBLE,
                    name="flight_exchange_not_possible",
                    description=self.why_not_exchangeable or "Flight exchange is not possible",
                )
            )

        return validation_issues


class ExchangeFlightAgent(WorkerAgent):
    def __init__(
        self,
        timezone: Optional[str],
        thread: ChatThread,
    ):
        self.timezone = timezone
        self.thread = thread

    async def validate(
        self,
        current_work_type: FrontOfHouseWorkAgentType,
        travel_context: TravelContext,
        user_preferences: ResponseAllPreferences,
        messages: list[BaseMessage],
    ) -> AgentValidationResponse:
        message_buffer_strs = get_message_buffer_as_strings(messages)
        existing_legs = None
        trip_id = travel_context.latest_flight_trip_id
        confirmation_id = travel_context.latest_flight_confirmation_id

        if trip_id and confirmation_id:
            try:
                _, existing_legs, _ = await FlightBamlHelper.get_flight_details_and_exchange_info(
                    trip_id,
                    confirmation_id,
                    FlightBamlHelper.get_exchangibiity,
                )
            except Exception as e:
                logger.error(
                    f"Failed to get flight details and exchange info for trip {trip_id} with confirmation id {confirmation_id}, error: {e}"
                )

        response = await b.CheckExchangeFlightEligibility(
            messages=message_buffer_strs,
            current_date=get_current_date_string(self.timezone),
            existing_legs=existing_legs,
        )

        return ExchangeFlightAgentResponse(
            able_to_exchange=response.able_to_exchange,
            why_not_exchangeable=response.why_not_exchangeable,
        )

    async def execute_plan(
        self,
        foh: "fha.FrontOfHouseAgent",
        classified_type_from_foh: FrontOfHouseWorkAgentType,
        messages: list[BaseMessage],
        extra_context: dict[str, Any],
        tool_call_id: str,
    ) -> dict[str, Any]:
        message_buffer_strs = get_message_buffer_as_strings(messages)
        return await handle_exchange_flight_v2(foh, message_buffer_strs, tool_call_id)
