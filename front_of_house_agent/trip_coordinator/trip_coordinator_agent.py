import asyncio
import copy
from functools import partial
from typing import Any, Coroutine

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
)

from agent.agent import Agent, StopResponse
from baml_client import b
from baml_client.types import ResponseAllPreferences, TripPlanningWorkType, UnifiedTaskType
from flight_agent.flights_helper import FlightBamlHelper
from front_of_house_agent.adapter import map_websocket_message
from front_of_house_agent.back_of_house_executor.flight_and_hotel_executor import TripPlanExecutor
from front_of_house_agent.common_models import TravelContext
from front_of_house_agent.sub_agent.trip_planner_sub_agent import handle_flight_planner, handle_hotel_planner
from front_of_house_agent.trip_coordinator.flight_worker import FlightWorker
from hotel_agent.hotels_helper import HotelsHelper
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.database.models.chat_thread import ChatThread
from server.database.models.user_profile import UserProfile
from server.schemas.authenticate.user import User
from server.services.memory.trips.retriever import TripMemoryRetriever
from server.utils.settings import AgentTypes, settings
from server.utils.websocket_no_op import partial_no_op_with_without_logger
from virtual_travel_agent.helpers import (
    get_current_date_string,
)
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory


class TripCoordinatorAgent(Agent):
    def __init__(
        self,
        user: User,
        user_profile: UserProfile | None,
        thread: ChatThread,
        websocket_send_message: partial[Coroutine[Any, Any, None]],
        timezone: str | None,
        flight_helper: FlightBamlHelper,
        hotel_helper: HotelsHelper,
        user_preferences: ResponseAllPreferences,
        travel_context: TravelContext,
        messages: list[BaseMessage],
    ):
        super().__init__(
            user=user,
            history=PostgresChatMessageHistory(thread_id=thread.id),
            mem_loader=TripMemoryRetriever(user_id=str(user.id), thread_id=str(thread.id)),
            websocket_send_message=websocket_send_message,
        )
        self.user_profile = user_profile
        self.thread = thread
        self.timezone = timezone
        self.flight_helper = flight_helper
        self.hotel_helper = hotel_helper
        self.user_preferences = user_preferences
        self.travel_context = travel_context
        self.email_mode = False
        self.messages = messages

        self.trip_plan_executor = TripPlanExecutor(
            flight_helper=flight_helper,
            message_sender=websocket_send_message,
            thread=thread,
            hotel_helper=hotel_helper,
            user=user,
            user_profile=user_profile,
            timezone=timezone,
        )

    async def persist_messages(self, messages: list[BaseMessage]):
        """Persist messages to history - interface compatibility with FrontOfHouseAgent."""
        if self.history:
            self.messages.extend(messages)
            for message in messages:
                self.history.add_pending_message(message)
            await self.history.apersist()

    async def run(self, message=None, message_type="text", extra_payload=None) -> list[Any]:
        message_buffer_strs = get_message_buffer_as_strings(self.messages)
        works = await b.NewSupervisorDoConverse(
            messages=message_buffer_strs,
            current_date=get_current_date_string(self.timezone),
            travel_context=self.travel_context.model_dump_json(exclude_none=True),
        )
        send = []
        send_websocket_messages = []
        for work in works.work_types or []:
            if work == TripPlanningWorkType.TripCoordination:
                res = await self.handle_trip_coordinator(
                    messages=self.messages,
                    extra_user_input=extra_payload or {},
                )
                send.extend(res)
        for m in send:
            if m:
                self.messages.append(m)
                self.add_history_message(m)
                send_websocket_messages += await map_websocket_message(
                    m,
                    self.travel_context.hotel_search_core_criteria.check_in_date,
                    self.travel_context.hotel_search_core_criteria.check_out_date,
                )
        await self.history.apersist()

        return send_websocket_messages

    async def persist_messages_in_memory(self, messages: list[BaseMessage]):
        self.messages.extend(messages)

    async def handle_trip_coordinator(
        self,
        messages: list[BaseMessage],
        extra_user_input: dict[str, Any],
    ) -> list[BaseMessage]:
        """Handle unified trip coordination for both planning and booking workflows."""
        while True:
            message_buffer_strs = get_message_buffer_as_strings(messages)
            trip_response = await self.process_streaming(
                partial(
                    b.stream.TripCoordinator,
                    travel_preference=self.user_preferences.model_dump_json(
                        exclude_none=True, exclude={k for k, v in self.user_preferences.model_dump().items() if v == []}
                    ),
                    messages=message_buffer_strs,
                    current_date=get_current_date_string(self.timezone),
                    self_intro=settings.OTTO_SELF_INTRO,
                    convo_style=settings.OTTO_CONVO_STYLE,
                    user_name=self.user.name,
                    flight_select_result=self.travel_context.flight_select_result.model_dump_json()
                    if self.travel_context.flight_select_result
                    else "{}",
                    hotel_select_result=self.travel_context.hotel_select_result.model_dump_json()
                    if self.travel_context.hotel_select_result
                    else "{}",
                    flight_validation_result=self.travel_context.flight_validation_result.model_dump_json()
                    if self.travel_context.flight_validation_result
                    else "{}",
                    hotel_validation_result=self.travel_context.hotel_validation_result.model_dump_json()
                    if self.travel_context.hotel_validation_result
                    else "{}",
                    flight_booking_status=self.get_flight_booking_status(self.travel_context),
                    hotel_booking_status=self.get_hotel_booking_status(self.travel_context),
                    web_search_knowledge=self.travel_context.web_search_knowledge or "",
                    trip_optimization_knowledge=self.travel_context.trip_optimization_knowledge or "",
                ),
                lambda x: x.agent_response,
                lambda x: x.agent_response,
            )
            if isinstance(trip_response, StopResponse):
                return [AIMessage(content=trip_response.last_text or "", additional_kwargs={"is_stopped": True})]

            coordinator_message = AIMessage(
                content=trip_response.agent_response or "",
                additional_kwargs={
                    "agent_classification": AgentTypes.FOH,
                },
            )

            if trip_response.tasks:
                flight_search_tasks = [
                    task for task in trip_response.tasks if task.task_type == UnifiedTaskType.FLIGHT_SEARCH
                ]
                hotel_tasks = [task for task in trip_response.tasks if task.task_type == UnifiedTaskType.HOTEL_SEARCH]
                flight_booking_tasks = [
                    task for task in trip_response.tasks if task.task_type == UnifiedTaskType.FLIGHT_BOOKING
                ]
                hotel_booking_tasks = [
                    task for task in trip_response.tasks if task.task_type == UnifiedTaskType.HOTEL_BOOKING
                ]

                web_search_tasks = [
                    task for task in trip_response.tasks if task.task_type == UnifiedTaskType.WEB_SEARCH
                ]
                trip_optimization_tasks = [
                    task for task in trip_response.tasks if task.task_type == UnifiedTaskType.TRIP_OPTIMIZATION
                ]

                if flight_search_tasks:
                    if len(flight_search_tasks) > 1:
                        flight_search_execution_tasks = []
                        for task in flight_search_tasks:
                            task_context = copy.deepcopy(self.travel_context)
                            if task_context and task_context.flight_search_core_criteria:
                                task_context.flight_search_core_criteria.outbound_date = task.outbound_date
                                if task.return_date:
                                    task_context.flight_search_core_criteria.return_date = task.return_date

                            async def wrap_handle_flight_plan(out_bound_date: str):
                                message_buffer_strs_copy = copy.deepcopy(message_buffer_strs)
                                message_buffer_strs_copy.append(
                                    f"user  let's start searching for flights on {out_bound_date}"
                                )
                                executor = TripPlanExecutor(
                                    flight_helper=self.flight_helper,
                                    message_sender=partial_no_op_with_without_logger,
                                    thread=self.thread,
                                    hotel_helper=self.hotel_helper,
                                    user=self.user,
                                    user_profile=self.user_profile,
                                    timezone=self.timezone,
                                )

                                flight_worker = FlightWorker(
                                    trip_plan_executor=executor,
                                )
                                return await flight_worker.handle_flight_planner(
                                    task_context,
                                    self.user_preferences,
                                    self.timezone,
                                    self.user,
                                    messages,
                                    message_buffer_strs_copy,
                                    extra_user_input,
                                    flight_search_core_criteria_str=task_context.flight_search_core_criteria.model_dump_json()
                                    if task_context and task_context.flight_search_core_criteria
                                    else "{}",
                                    flight_search_additional_criteria_str="{}",
                                    flight_select_result_str="{}",
                                    thread=self.thread,
                                    persist_messages=self.persist_messages_in_memory,
                                    prompt_user_preferences=None,
                                )

                            flight_search_execution_tasks.append(wrap_handle_flight_plan(task.outbound_date or ""))

                        if flight_search_execution_tasks:
                            await self.websocket_send_message(
                                message={
                                    "type": "flights_skeleton_async",
                                    "isBotMessage": True,
                                    "expectResponse": False,
                                }
                            )

                        flight_results = await asyncio.gather(*flight_search_execution_tasks)

                        return [coordinator_message, *flight_results]

                    else:
                        res = await handle_flight_planner(
                            self,
                            messages,
                            message_buffer_strs,
                            extra_user_input,
                            flight_search_core_criteria_str="{}",
                            flight_search_additional_criteria_str="{}",
                            flight_select_result_str="{}",
                            prompt_user_preferences=None,
                            streaming=True,
                        )
                        return res["messages"]

                elif hotel_tasks:
                    res = await handle_hotel_planner(
                        self,
                        messages,
                        message_buffer_strs,
                        self.travel_context,
                        extra_user_input,
                        prompt_user_preferences=None,
                        streaming=True,
                    )
                    return res["messages"]

                elif flight_booking_tasks:
                    res = await self.execute_flight_booking_workflow(
                        messages, message_buffer_strs, extra_user_input, True
                    )
                    return res["messages"]

                elif hotel_booking_tasks:
                    res = await self.execute_hotel_booking_workflow(
                        messages, message_buffer_strs, extra_user_input, True
                    )
                    return res["messages"]
                elif web_search_tasks:
                    await self.websocket_send_message(
                        message={
                            "type": "prompt",
                            "isBotMessage": True,
                            "expectResponse": False,
                            "text": coordinator_message.content,
                        }
                    )
                    await self.persist_messages([coordinator_message])
                    # Handle web search tasks if any
                    await self.websocket_send_message(
                        message={
                            "type": "search_update",
                            "isBotMessage": True,
                            "expectResponse": False,
                            "text": "Performing web search tasks...",
                        }
                    )
                    web_search_execution_tasks = []
                    for task in web_search_tasks:
                        if task.query:
                            web_search_execution_tasks.append(
                                b.WebSearch(
                                    query=task.query,
                                    messages=message_buffer_strs,
                                    current_date=get_current_date_string(self.timezone),
                                )
                            )
                    search_result = await asyncio.gather(*web_search_execution_tasks)
                    search_result_string = "\n".join([result for result in search_result if result])
                    # Store web search knowledge in TravelContext
                    self.travel_context.web_search_knowledge = search_result_string
                    m = AIMessage(
                        content=search_result_string,
                    )
                    await self.persist_messages([m])
                    websocket_m = (
                        await map_websocket_message(
                            m,
                            self.travel_context.hotel_search_core_criteria.check_in_date,
                            self.travel_context.hotel_search_core_criteria.check_out_date,
                        )
                    )[0]
                    await self.websocket_send_message(message=websocket_m)

                elif trip_optimization_tasks:
                    await self.websocket_send_message(
                        message={
                            "type": "prompt",
                            "isBotMessage": True,
                            "expectResponse": False,
                            "text": coordinator_message.content,
                        }
                    )
                    # Handle trip optimization tasks if any
                    await self.persist_messages([coordinator_message])
                    await self.websocket_send_message(
                        message={
                            "type": "search_update",
                            "isBotMessage": True,
                            "expectResponse": False,
                            "text": "Performing trip optimization tasks, determining the best itinerary...",
                        }
                    )

                    # Execute trip optimization tasks
                    trip_optimization_execution_tasks = []
                    for task in trip_optimization_tasks:
                        trip_optimization_execution_tasks.append(
                            b.TripOptimization(
                                travel_preference=self.user_preferences.model_dump_json(),
                                messages=message_buffer_strs,
                                current_date=get_current_date_string(self.timezone),
                            )
                        )
                    optimization_result = await asyncio.gather(*trip_optimization_execution_tasks)
                    optimization_result_string = "\n".join([result for result in optimization_result if result])
                    # Store trip optimization knowledge in TravelContext
                    self.travel_context.trip_optimization_knowledge = optimization_result_string

                    m = AIMessage(
                        content=optimization_result_string,
                    )
                    await self.persist_messages([m])
                    websocket_m = (
                        await map_websocket_message(
                            m,
                            self.travel_context.hotel_search_core_criteria.check_in_date,
                            self.travel_context.hotel_search_core_criteria.check_out_date,
                        )
                    )[0]
                    await self.websocket_send_message(message=websocket_m)

                else:
                    # No specific tasks to perform, just return the coordinator message
                    return [coordinator_message]
            else:
                # No tasks to perform, just return the coordinator message
                return [coordinator_message]

    async def execute_flight_booking_workflow(
        self,
        messages: list[BaseMessage],
        message_buffer_strs: list[str],
        extra_user_input: dict[str, Any],
        streaming: bool = True,
    ) -> dict[str, Any]:
        """Execute flight booking workflow using existing TripPlanExecutor."""
        travel_context = self.travel_context
        if not travel_context or not travel_context.flight_select_result:
            error_message = AIMessage(
                content="No flight selection found. Please select a flight first through trip planning.",
                additional_kwargs={"agent_classification": AgentTypes.FOH},
            )
            return {
                "messages": [error_message],
                "current_topic": "TripCoordination",
                "model": None,
            }

        return await handle_flight_planner(
            self,
            messages,
            message_buffer_strs,
            extra_user_input,
            travel_context.flight_search_core_criteria.model_dump_json(exclude_none=True)
            if travel_context.flight_search_core_criteria
            else "{}",
            travel_context.flight_search_additional_criteria.model_dump_json(exclude_none=True)
            if travel_context.flight_search_additional_criteria
            else "{}",
            travel_context.flight_select_result.model_dump_json() if travel_context.flight_select_result else "{}",
            None,
            streaming=streaming,
        )

    async def execute_hotel_booking_workflow(
        self,
        messages: list[BaseMessage],
        message_buffer_strs: list[str],
        extra_user_input: dict[str, Any],
        streaming: bool = True,
    ) -> dict[str, Any]:
        """Execute hotel booking workflow using existing TripPlanExecutor."""
        travel_context = self.travel_context
        if not travel_context or not travel_context.hotel_select_result:
            error_message = AIMessage(
                content="No hotel selection found. Please select a hotel first through trip planning.",
                additional_kwargs={"agent_classification": AgentTypes.FOH},
            )
            return {
                "messages": [error_message],
                "current_topic": "TripCoordination",
                "model": None,
            }

        return await handle_hotel_planner(
            self,
            messages,
            message_buffer_strs,
            travel_context,
            extra_user_input,
            None,
            streaming=streaming,
        )

    def get_flight_booking_status(self, travel_context: TravelContext) -> str:
        """Get current flight booking status."""
        if not travel_context or not travel_context.flight_select_result:
            return "not_selected"
        if travel_context.flight_validation_result:
            return "validated"
        if travel_context.flight_select_result.selected_outbound_flight_id:
            return "selected"
        return "not_selected"

    def get_hotel_booking_status(self, travel_context: TravelContext) -> str:
        """Get current hotel booking status."""
        if not travel_context or not travel_context.hotel_select_result:
            return "not_selected"
        if travel_context.hotel_validation_result:
            return "validated"
        if travel_context.hotel_select_result.property_id:
            return "selected"
        return "not_selected"
