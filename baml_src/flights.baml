class FrequentFlierNumber {
  airline_code string @description("The IATA code for the airline the frequent flyer number is associated with.")
  number string @description("The frequent flyer number.")
}

class FlightBookingResponse {
  agent_response string @description("a message to the traveler confirming the flight booking and the airline_confirmation_number if available")
  error_response string? @description(#"If there was an error_response provided then respond back to the traveler in a friendly tone that the flights couldn't be booked and to try again later. Also include an error_response text extracted summarizing the error. e.g. 'The selected itinerary is no longer available. Please try start search from beginning again.'
    please ignore warning if the status is success.
  "#)
}

class ChangeFlight {
  start_ariport_code string @description("The IATA code for the airport the traveler want to change for the flight as start airport.")
  end_airport_code string @description("The IATA code for the airport the traveler want to change for the flight as end airport.")
  date string @description("The date the traveler want to change for the flgiht to be on. It is ISO 8601 format, yyyy-mm-ddThh:mm:ss.")
  is_newly_add bool @description("True if this flight is newly added, otherwise False. It is usually happend to add return flight for one way flgiht.")
}

template_string ProcessFlightSearchRules(preferred_flight_cabin: string?) #"
    ---
    **Flight Selection Order:**
    **Step 1: Honor the user's explicitly stated request in this order:**
      1. **Fare class**  → Pick {{ preferred_flight_cabin | default("ECONOMY") }} if available.
      2. **Airline** → If not specified, check user preference, if user preference of airline is also not avaiable, check the default airline codes for the departing airport. 
      3. **Stops** → If the user explicitly requests a stopover (e.g., "I want a stop in Chicago"), honor that **even if their saved preference is nonstop**.
      4. **Departure time window** (if specified) → Strictly enforce this preference.
      5. **Arrival time window** (if specified).

    **Step 2: If no explicit request is given, honor user preferences:**
      1. **Fare class** → Pick {{ preferred_flight_cabin | default("ECONOMY") }} if available.
      2. **Preferred airlines** (saved preferences).
      3. **Stops** → If none, default to **Nonstop**.

    **Step 3: If no user preferences exist, use built-in business traveler assumptions:**
      1. **Fare class** → Pick {{ preferred_flight_cabin | default("ECONOMY") }} if available.
      2. **Airline:** Prioritize 'home-airlines' based on departure airport.
      3. **Stops:** Default to Nonstop.

    **Step 4: Ensure a total of six unique flight options are always provided**
    - **First, select all non-stop flights (0-stop), ensuring only one option per flight number.**
      - If multiple fare classes exist for the same flight, pick **only one** using this priority:
        1. **Exact match to user request** (if specified).
        2. **Preferred fare class (if provided)**.
        3. **Lowest cost if no clear preference**.
    - **Ensure every selected flight has a unique flight number at all cost.**
      - If two options with the same flight number exist, **keep only the highest-ranked option and replace the duplicate with a different flight number.**
      - You need to dedup by using the 'dedup_key' field in the data strictly.
    - **If no non-stop flights exist, explicitly inform the traveler but continue selecting the best alternatives.**
    - **Then, if fewer than six options are available, add layover flights that match at least one preference:**
      1. **Same airline** (if a preferred airline exists).
      2. **Same fare class** (e.g., Business if requested).
      3. **Shortest layovers** (minimizing total travel time).

    **Step 5: Rank the selected flights by below order:**
      1. Place flights that best match the user’s specific preferences at the top. 
        This includes: cabin, stops, airline/alliance, departure/arrival time window, cancellation policy
        Note: use the departure_time or arrival_time fields in the data to determine if the flight matches the user's time preferences.
      2. Among flights with the same level of preference match, rank by **Price** (lowest to highest)
      3. If there is still a tie, sort by **duration** (shortest to longest)
      NOTE:Don’t consider any other criteria (eg. convienience unless user explicitly asks) if it is listed above.
"#


class SelectedFlight {
  reason string @description(#"
    Summarize the reason why this flight is selected than others in the csv data.
    Highlight its key advantages COMPARED to other flights in categories in separate lines.
    each bullet point should be 10 words or less and keep the top 3 lines.
    Put each point in a separate line, and DO NOT start with any symbols like '*', '-', or '•'.
    The reason should be compelling and highlight its key advantages, also please take business traveler common knowledge into consideration when generating reason. 
    If no exact match was found for user preferences, explain why this alternative was chosen despite not being optimal.

    - ONLY mention that the traveler can accumulate mileage on their loyalty program IF:
      - The selected flight is operated by an airline for which the traveler has provided a loyalty program number, OR
      - The selected flight is operated by an airline within the same alliance as the loyalty program provided (see Alliance Info section), AND the traveler's frequent flyer program allows accrual with that airline.
    - DO NOT mention mileage accrual if the traveler's loyalty program airline and the selected flight's airline are not in the same alliance.
    - DO NOT mention mileage accrual for airlines outside of the traveler's stated loyalty programs or their alliances, even if the airline is a codeshare partner or has a bilateral agreement, unless explicitly supported.
    - If there is any ambiguity about alliance partnerships or mileage eligibility, err on the side of NOT mentioning mileage accrual.
  "#)
  // why string? @description(#"
  //   why you include accumulate mileage on your Delta program in reason, this is not delta flight, and not aliiance, where you get the info?

  // "#)  
  index_id int @description("The index_id of the preferred flight. Return the field 'index_id' from the input csv data.")
}

class FlightSearchResponse {
  fc_1 SelectedFlight | null @alias(first_choice) @description("The first choice of the preferred flight.")
  fc_2 SelectedFlight | null @alias(second_choice) @description("The second choice of the preferred flight.")
  fc_3 SelectedFlight | null @alias(third_choice) @description("The third choice of the preferred flight.")
  fc_4 SelectedFlight | null @alias(fourth_choice) @description("The fourth choice of the preferred flight.")
  fc_5 SelectedFlight | null @alias(fifth_choice) @description("The fifth choice of the preferred flight.")
  fc_6 SelectedFlight | null @alias(sixth_choice) @description("The sixth choice of the preferred flight.")

  error_response string? @description("An error message back to the traveler in a friendly tone when no flight choices were returned and that they could try changing their search criteria or try again later.")
  @@dynamic
  //For debug
  // explain string[] @description("Why AS airline rank before HA airline?")
}

template_string PremiumFlightSelectRules(preferred_flight_cabin: string?) #"
    **Step 2: Selection based on priority (in strict order):**
        1. **Fare Class**
          - Must be {{ preferred_flight_cabin |  default("BUSINESS") }} class if available.
          - If unavailable, offer next best option.
        2. **Flight Stops**
          - Prioritize non-stop flights
          - Only consider flights with stops if fewer than 6 non-stop options exist or user requests
        3. **Airline Selection:**
          - Only consider specific airlines if explicitly requested
          - Ignore airline loyalty/alliances unless specifically mentioned
        4. **Time Window**
          - If user specifies a preferred departure or arrival time window, select flights that match this window as the primary options.
          - Additionally, if there are fewer than 6 flights matching the time window, include a few alternatives that fall outside this time window in case user wants flexibility.
	        - If no time preference is given, select flights across different parts of the day (e.g., morning, afternoon, evening) to give user a varied set of options.

      **If No Matches Found:**
        1. First try: closest alternatives that are exchangeable
        2. Second try: best available exchangeable options
        3. Always include explanation for why perfect matches weren't available
"#

template_string BasicFlightSelectRules(preferred_flight_cabin: string?) #"
    **Step 2: Flight Selection Process**
      **Selection Criteria:**
        **A. Process explicit user requests (in strict order, but if user mentions multiple criteria, treat them as equal priority)**
          1. **Fare Class**: {{ preferred_flight_cabin | default("ECONOMY") }}
            - If the requested fare class is unavailable, offer the next best alternative fare class. eg. if prenium economy is not available, offer economy. *Never offer BASIC_ECONOMY as an alternative.*
          2. **Airline**: 
          - Check in below order:
            - Explicitly specified airline
            - User's airline preference
            - Default airline codes for departure airport
          3. **Stops**: Honor specific stopover requests (e.g., "stop in Chicago")
            - Override saved nonstop preferences if stopover explicitly requested
          4. **Cancellation Policy**:
            - If user explicitly requests refundable flights, prioritize refundable options.
            - If no explicit request, treat non-refundable and refundable as equivalent.            
          5. **Time Windows**:
            - If user specifies a preferred departure or arrival time window, select flights that match this window as the primary options.
            - Additionally, if there are fewer than 6 flights matching the time window, include a few alternatives that fall outside this time window in case user wants flexibility.
            - If no time window specified, and other creteria are tied for selected flights, select the flight that departs in different time of the day.

        **B. Handle No Explicit Requests (use saved preferences)**
          1. **Fare class** → Pick {{ preferred_flight_cabin | default("ECONOMY") }} if available.
          2. **Preferred airlines** (saved preferences).
          3. **Stops** → If none, default to **Nonstop**.

        **C. Default business rules (if no preferences exist)**
          1. **Fare class** → Pick {{ preferred_flight_cabin | default("ECONOMY") }} if available.
          2. **Airline:** Prioritize 'home-airlines' based on departure airport.
          3. **Stops:** Default to Nonstop.
          4. **Airport:** If user requests a city as destination and there are multiple airports in the city, try to select the closer airport, and mention this as reason in the response.
      
      **Finial Selection Rules:**
      - Maximum selections: Up to 6 unique flights from the ranked list. 
      - Show all matching flights (even if only 1-2 flights qualify)
      - Only expand selection criteria if ZERO flights found
      - If MORE THAN ONE fares for same flight are found, maintain original strict criteria
        - **Ensure select AT MOST 1 fare option per flight at all cost.**
        - If MORE THAN ONE fares with the same flight exist, keep the lowest price fare option.
      - **If no non-stop flights exist, explicitly inform the traveler but continue selecting the best alternatives.**
      - **Then, if fewer than six options are available, add layover flights that match at least one preference:**
        1. **Same airline** (if a preferred airline exists).
        2. **Same fare class** (e.g., Business if requested).
        3. **Shortest layovers** (minimizing total travel time).
"#

template_string FlightRankingRules() #"
    Rank the selected flights by below order:**
      1. Place flights that best match the user's specific preferences at the top. 
        This includes: cabin, stops, airline/alliance, departure/arrival time window, cancellation policy
        Note: use the departure_time or arrival_time fields in the data to determine if the flight matches the user's time preferences.
      2. Among flights with the same level of preference match, rank by **Price** (lowest to highest), the flights for selection are already sorted by price (lowest to highest), the one with the lowest price should be the first one.
      3. If there is still a tie, sort by **duration** (shortest to longest)
      4. If departure/arrival time is specified but no flights excatly match the time window, put the flights that are closest to the time window before other flights.
      NOTE: Don't consider any other criteria (eg. convienience unless user explicitly asks) if it is listed above.
"#

template_string AirlineKnowledge() #"
    **Airline Knowledge:**
    - Some fare class names vary by airline:
      - Delta Airline Fare Class Mapping:
          - Delta Main Basic: Basic Economy 
          - Delta Main/ Delta Main Classic/ Delta Main Extra: Economy
          - Delta Comfort: Premium Economy
          - Delta Premium Select: Premium Economy
          - Delta First: First Class (domestic or select international)
          - Delta One: Business Class (international and select transcontinental)
      - JetBlue Fare Class Mapping:
        - Blue Basic: Basic Economy
        - Blue / Blue Plus/ Blue Extra: Economy
        - Mint: Business Class
"#

template_string BusinessTravelerKnowledge() #"
    **Business Travel Common Knowledge**

    - **Punctuality is critical:** Business travelers generally value on-time performance and minimal risk of delays or missed connections, as their schedules are often tight.
    - **Schedule flexibility matters:** Options that allow changes (i.e., not basic economy) are strongly preferred, given the possibility of last-minute meeting changes or emergencies.
    - **Time Windows:** Business travelers often have specific time windows for departure and arrival -- usually as depart after 9Am, arrive before 7Pm.
    - **Loyalty and status:** Many business travelers belong to frequent flyer programs; flights on airlines within their loyalty program are preferred for mileage accrual and elite benefits.
    - **Amenities and comfort:** Access to priority boarding, extra legroom, Wi-Fi, power outlets, and in-cabin workspace can be important for productivity and comfort.
    - **Baggage allowance:** Business travelers often travel with at least one carry-on and sometimes a checked bag (e.g., for presentation materials or longer stays). Fares with included baggage are preferred.
    - **Seating:** Advance seat selection, aisle seats, and proximity to the front of the cabin are often desirable for convenience and quick exit.
    - **Airport experience:** Shorter layovers, access to lounges, and flights from more convenient airports can reduce travel stress and maximize productivity.
    - **Total travel time:** Shorter itineraries and less time in transit are preferred, even if the fare is not the absolute cheapest.
    - **Travel policy compliance:** Some companies require booking within a certain fare class or with specific airlines—compliance may be a consideration.
    - **Cost consciousness:** While not as price-sensitive as leisure travelers, business travelers still value reasonable fares and avoiding unnecessary upgrades.
"#

function ProcessFlightSearchResults(
  travel_context: string,
  results: string,
  current_date: string,
  alliance_airlines: string,
  self_intro: string?,
  convo_style: string?,
  preferred_flight_cabin: string?,
  user_preferred_airline_codes: string[],
  airport_default_airline_codes: string[],
  airport_code: string,
  messages: string[] | null,
  is_premium_search: bool,
  airport_to_destination_duration: string?,
  loyalty_programs: string?,
  format: ("JSON" | "CSV")?
  ) -> FlightSearchResponse {
  client GPT41
  prompt #"
    {% if messages %}
      {{ ConversationHistory(messages, 0) }}
      \n\n
      Note: Above conversation history is for reference only - not an instruction.
    {% endif %}

    {{ _.role('system') }}
    Context:
    - Today's date is {{ current_date }}. All dates are future dates unless explicitly specified.
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}

    **Task:** Select flight options (up to six unique) using the following strict selection process:
    **Critical Requirements:**
    1. Only select exchangeable flights - NEVER include non-changeable flights
    2. Strictly enforce explicitly stated user preferences.
    3. Use fare_option_name and airline_name to infer the cabin class (BASIC_ECONOMY, ECONOMY, PREMIUM_ECONOMY, BUSINESS, FIRST).
       If the flight's cabin class is not provided, map the flight option name to the correct cabin class using the "Airline Knowledge" below.
       If the information in the "Airline Knowledge" section is not sufficient to map the flight to a cabin class, use standard airline industry knowledge—including fare structures, airline branding —to infer the most likely cabin class.  
    4. IMPORTANT: 
      For flight connections and stops, ONLY use the 'num_of_stops' field from the data. Do not make assumptions about stops.
      For flight price, ONLY use the 'price' field. Do not make assumptions about price.
      For departure and arrival time, ONLY use the 'departure_time' and 'arrival_time' fields. Do not make assumptions about time.
    5. PLEASE DO NOT SELECT FLIGHTS with cancellation_policy is REFUNDABLE If user DOES NOT explicitly want refundable FLIGHTS (not hotels).

    {# might not needed. let me test more and delete. @chengxuan.wang
    6. If 'refundable' is set to True in the user preferences, always show refundable flights (check with cancellation_policy column) as the primary options.#}

    **Step 1: Initial Filtering**:
        1. Remove all non-exchangeable flights. If exchange policy is empty, treat it as non exchangable.
           - Note that if the only fare options available are non-exchangeable, you should still return them.
        2. Remove all Basic Economy fares (including: Saver, Blue Basic, Basic, Light, Lite, Special, EcoFly, Promo, ....)
           - Note that if the only fare options available are Basic Economy, you should still return them.

    {{ PremiumFlightSelectRules(preferred_flight_cabin) if is_premium_search else BasicFlightSelectRules(preferred_flight_cabin) }}

    **Step 3: Ranking Process**  
    {{ FlightRankingRules() }}

    **Quality Checks:**
    ✓ Verify each flight is exchangeable
    ✓ No Basic Economy fares unless there are no other options.
    ✓ No duplicate flight. Make sure at most 1 fare option per flight number is selected in the final results STRICTLY.
 
    **MAKE SURE**
      IMPORTANT: 
      - Do not return empty flight results at best efforts with satisfying the above rules. Try to find as many flights (max 6) as possible.
      {% if format == "JSON" %}
      - You must select flights only from the JSON data located strictly between the lines marked ##### BEGIN FLIGHT DATA (JSON) and ##### END FLIGHT DATA (JSON). Do not select, invent, or suggest any flights not found within this exact section, even if referenced earlier or remembered from previous results.
      {% else %}
      - You must select flights only from the CSV data located strictly between the lines marked ##### BEGIN FLIGHT DATA (CSV) and ##### END FLIGHT DATA (CSV). Do not select, invent, or suggest any flights not found within this exact section, even if referenced earlier or remembered from previous results.
      {% endif %}
      - No duplicate flight. Make sure at most 1 fare option per flight is selected in the final results.
    
    ------
    {% if format == "JSON" %}
    **Flight Data for Selection (in JSON format):**
    ##### BEGIN FLIGHT DATA (JSON) 
    {{ results }}
    ##### END FLIGHT DATA (JSON)
    Note: Each item contains a "flight" object with flight details and a "fares" array with fare options. Times use 24-hour format (e.g., 11:00 = 11 AM, 23:00 = 11 PM).
    {% else %}
    **Flight Data for Selection (in csv format):**
    ##### BEGIN FLIGHT DATA (CSV) 
    {{ results }}
    ##### END FLIGHT DATA (CSV)
    Note: Times use 24-hour format (e.g., 11:00 = 11 AM, 23:00 = 11 PM). you can refer depature_time_category and arrival_time_category for summarized time categories of the flight.
    {% endif %}
    IMPORTANT: You are strictly prohibited from suggesting any flight that is not present in above data section. All results must be from the provided data only.

    {{ AirlineKnowledge() }}

    {{ BusinessTravelerKnowledge() }}

    **Alliance Info:**
    The format is: Alliance Name -> Partner Airlines (IATA codes)
    {{ alliance_airlines }}

    **Preferences:**
    {{ travel_context }}
    Time format: HH-HH (24-hour, e.g., 08-12 = 8 AM to 12 PM)

    **Cabin Class:**
    {{ preferred_flight_cabin |  default("BUSINESS" if is_premium_search else "ECONOMY")  }}

    **Traveler preferred airline codes:** 
    {{ user_preferred_airline_codes }}

    **Default airline codes for the airport:**
    {{ airport_default_airline_codes }}

    **Airport:**
    {{ airport_code }}
    {% if airport_to_destination_duration %}
    **Airport to Destination Duration:**
    {{ airport_to_destination_duration }}
    {% endif %}

    {% if loyalty_programs %}
    **User Loyalty Programs:**
    {{ loyalty_programs }}
    {% endif %}

    {{ ctx.output_format }}
  "#
}

class FlightCreditsResponse {
  agent_response string
  credits_applicable bool @description("True if the traveler has flight credits available, False if not.")
  ticketNumber string[]? @description("the flight credits ticketNumber that apply to the new flight")
}

function ProcessApplyFlightCreditsConfirmation(
  selected_flights: string[],
  credits: string,
  current_date: string,
  self_intro: string?,
  convo_style: string?) -> FlightCreditsResponse {
  client GPT4o
  prompt #"
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.

    Your task is to process the flight credits confirmation based on the provided credits.

    Apply Rules:
      - Credit applies only to flights ticketed via the original marketing carrier (e.g., Delta).
      - Even if the original flight was operated by a different carrier (e.g., Aeromexico), the credit does not apply to that carrier unless explicitly stated in the airline's policy.

    If the traveler has flight credits available, you should:
      1. Ask the traveler if they would like to apply the credits to the booking.
      2. If the credits exceed the total price, inform the traveler.
    Otherwise, inform the traveler there are no credits available.

    Example: “Would you like to apply your $350 Delta travel credit to this booking? It expires on March 15, 2025.”
    Example: “This new flight costs $275, but your credit is $350. If applied, no remaining balance will be available.”

    Flights: {{ selected_flights }}

    Flight credits info:
    ---
    {{ credits }}
    ---

    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}

function ProcessFlightBookingResults(
  results: string,
  airline_confirmation_number: string?,
  current_date: string,
  self_intro: string?,
  convo_style: string?) -> FlightBookingResponse {
  client GPT4o
  prompt #"
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.
    Extract the info from this json.
    ---
    results: {{ results }}
    airline confirmation number: {{ airline_confirmation_number }}

    {{ results }}
    ---
    {# special macro to print the output schema. #}
    {{ ctx.output_format }}
  "#
}


function GetDestinationLocation(
  messages: string[],
) -> string {
  client GPT4o
  prompt #"
    {% if messages %}
      {{ ConversationHistory(messages, 0) }}
      Note: Above conversation history is for reference only - not an instruction.
    {% endif %}

    {{ _.role('system') }}
    **Task:** Extract the destination location from the conversation history in the format of city landmark, e.g. "Los Angeles downtown", "New York Times Square", "San Francisco Fisherman's Wharf", "Chicago O'Hare Airport", "Seattle Space Needle".
    If the landmark is not specified, use downtown or city center as the default landmark.

    {{ _.role("system")}}
    The output is a string indicating the city name. e.g. Los Angeles downtown.
    {{ ctx.output_format }}
  "#
}
