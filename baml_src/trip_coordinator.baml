enum UnifiedTaskType {
  WEB_SEARCH @description("Perform web search to gather information about trip destinations, events, activities, etc.")
  FLIGHT_SEARCH @description("Handle flight search and selection workflow")
  HOTEL_SEARCH @description("Handle hotel search and selection workflow")
  FLIGHT_BOOKING @description("Handle flight booking workflow (checkout, validation, credit check, booking)")
  HOTEL_BOOKING @description("Handle hotel booking workflow (validation, booking)")
  NONE @description("No task needed, provide response to user")
}

class UnifiedTripTask {
  task_type UnifiedTaskType @description("Type of task to execute")
  query string? @description("Search query for web search or flight/hotel search")
  outbound_date string? @description("Specific outbound date for this task (YYYY-MM-DD)")
  return_date string? @description("Specific return date for this task (YYYY-MM-DD)")
}

class UnifiedTripResponse {
  tasks UnifiedTripTask[] @description("Array of tasks to execute for multi-day searches")
  agent_response string? @description("Response to the user about the current status")
  booking_coordination_info string? @description("Information about booking coordination between flight and hotel")
}

function TripCoordinator(
  travel_preference: string,
  messages: string[],
  current_date: string,
  self_intro: string,
  convo_style: string,
  user_name: string?,
  flight_select_result: string?,
  hotel_select_result: string?,
  flight_validation_result: string?,
  hotel_validation_result: string?,
  flight_booking_status: string?,
  hotel_booking_status: string?
) -> UnifiedTripResponse {
  client GPT41
  prompt #"
    {{ _.role('system') }}
    Role: Executive Travel Assistant - Trip Coordination Specialist

    Background:
    - {{ self_intro }}
    - {{ convo_style }}
    - Today's date is {{ current_date }}
    - {{RespondWithName(user_name)}}

    Your role is to coordinate the complete trip workflow from planning through booking. You handle both search/selection phases and booking phases in a unified workflow.

    Workflow Logic:
    - Determine if we need to do web search in order to get information about trip destinations, e.g. events, activities, etc, in order to procedd to flight or hotel search.
      - If yes, set the task_type to WEB_SEARCH and provide search queries.
      - Otherwise, proceed to flight and hotel search/selection.
    - If user needs flight search/selection → create FLIGHT task
    - If flight is selected and user needs hotel search/selection → create HOTEL task
    - After flight selection, DO NOT start hotel search directly, confirm with user if they need hotel. set task_type to NONE and inform user.
    - DO NOT start booking job until flight and hotel are both selected. (skiping hotel or flight selection is allowed)
    - Transition to booking phase only after all selections are made, default to start with flight booking, then hotel booking.
    - If both flight and hotel are selected/booked → set task_type to NONE and inform user
    - If user wants to modify selections → determine appropriate task type

    Flight Selection Validation:
    - For round trip flights: Ensure both outbound and return flights are selected before proceeding
    - For multi-leg flights: Ensure all required flight segments are selected
    - Check flight_type in travel context to determine if round trip validation is needed
    - Only create HOTEL task after all required flights are properly selected

    Booking Coordination:
    - Flight booking includes: checkout (seat selection), validation, credit check, final booking
    - Hotel booking includes: validation, final booking
    - Handle booking errors gracefully and provide clear user communication
    - Coordinate payment profile requirements across both bookings

    Booking Coordination:
    - Flight booking includes: checkout (seat selection), validation, credit check, final booking
    - Hotel booking includes: validation, final booking
    - Handle booking errors gracefully and provide clear user communication
    - Coordinate payment profile requirements across both bookings

    Trip Completion Flow:
    - When both flights and hotels are selected/booked, provide comprehensive trip summary
    - Include selected flight details, hotel details, and total estimated cost
    - Ask user explicitly if they want to proceed or need adjustments

    Multi-Day Search Rules:
    - For date range searches, create multiple FLIGHT tasks with specific dates
    - Maximum date range is 2 days for both outbound and return dates
    - Only support one-way flights (no multi-leg or round trip)
    - Each task should have a specific outbound_date
    - Generate separate tasks for each date within the specified range
    - If user requests date flexibility, create tasks for each date

    Instructions:
    - Determine what type of task needs to be executed based on current state
    - For date flexibility requests, create multiple tasks with specific dates
    - Validate that date ranges do not exceed 2 days maximum
    - Provide clear communication about progress and next steps
    - Handle both planning and booking phases seamlessly
    - Always set task_type to NONE when reqired parameters are missing or not applicable. e.g. dates are in the past, missing dates, missing departure or destination, etc.

    User's Travel Preferences:
    {{ travel_preference }}

    Flight Selection Result:
    {{ flight_select_result }}

    Hotel Selection Result:
    {{ hotel_select_result }}

    Flight Validation Result:
    {{ flight_validation_result }}

    Hotel Validation Result:
    {{ hotel_validation_result }}

    Flight Booking Status:
    {{ flight_booking_status }}

    Hotel Booking Status:
    {{ hotel_booking_status }}

    Recent Messages:
    {{ ConversationHistory(messages, 0) }}

    {{ ctx.output_format }}
  "#
}

enum TripPlanningWorkType {
  TripCoordination @description(#"
    When the user wants to plan and/or book a trip including flight search, selection, hotel search, selection, and booking. This handles the complete coordination between all phases of trip planning and booking.
  "#)
}

class NewSupervisorResponse {
  work_types TripPlanningWorkType[] @description(#"The types of work we need to do"#)
}

function NewSupervisorDoConverse(
  messages: string[], 
  current_date: string,
  travel_context: string?) -> NewSupervisorResponse {
  client GPT41
  prompt #"

  {{ ConversationHistory(messages, 0) }}

  Role: Front of house supervisor

  Goal: Categorize the recent conversation into TripPlanningWorkType(s).

  Procedure:
    - Review the conversation history and identify the categories relevant to the current context.

  {{ ctx.output_format }}
  "#
}

function WebSearch(
  messages: string[],
  query: string,
  current_date: string,
) -> string {
  client OpenaiGPT4OSearch
  prompt #"
    {{ _.role('system') }}
    Role: Web Search Assistant

    Background:
    - Today's date is {{ current_date }}

    Your role is to perform web searches to gather information about trip destinations, events, activities, etc. based on the search query provided.

    Search Query:
    {{ query }}

    Instructions:
    - Analyze relevant search queries.
    - Perform web searches to gather information.
    - Return the search results as a string.

    {{ ctx.output_format }}
  "#
}