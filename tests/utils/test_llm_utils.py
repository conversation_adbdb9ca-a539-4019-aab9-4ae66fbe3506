import re
from datetime import datetime

from llm_utils.llm_utils import reconcile_llm_inferred_airline_iata_code
from virtual_travel_agent.helpers import get_current_date_string


def test_reconcile_llm_inferred_airline_iata_code():
    assert reconcile_llm_inferred_airline_iata_code("AA") == "AA"
    assert reconcile_llm_inferred_airline_iata_code("WN") == "WN"

    assert reconcile_llm_inferred_airline_iata_code("Frontier") == "F9"
    assert reconcile_llm_inferred_airline_iata_code("frontier") == "F9"
    assert reconcile_llm_inferred_airline_iata_code("Delta Air lines") == "DL"
    assert reconcile_llm_inferred_airline_iata_code("South west") == "WN"
    assert reconcile_llm_inferred_airline_iata_code("Southwest") == "WN"
    assert reconcile_llm_inferred_airline_iata_code("Southwest airline") == "WN"


def test_get_current_date_string():
    # Test without timezone
    result = get_current_date_string()

    # Should match format: "Monday, January 15th 2025"
    pattern = r"^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (January|February|March|April|May|June|July|August|September|October|November|December) \d{1,2}(st|nd|rd|th) \d{4}$"
    assert re.match(pattern, result), f"Date string '{result}' does not match expected format"

    # Test with timezone
    result_with_tz = get_current_date_string("America/New_York")
    assert re.match(
        pattern, result_with_tz
    ), f"Date string with timezone '{result_with_tz}' does not match expected format"

    # Verify the day of week is correct for current date
    now = datetime.now()
    expected_day = now.strftime("%A")
    assert result.startswith(expected_day), f"Expected day '{expected_day}' not found at start of '{result}'"
