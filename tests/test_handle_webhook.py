import json

import pytest

from server.schemas.partners.spotnana.travel_delivery import SpotnanaTravelDelivery
from server.services.partners.spotnana.webhook_event_handler import handle_spotnana_webhook_event

exchange_event = {
    "timestamp": "2025-05-07T17:49:21.514635634Z",
    "event_type": "PNR_V3",
    "operation": "BOOKING_TICKETED",
    "payload": {
        "version": 14,
        "createdVia": "OBT",
        "initialVersionCreatedVia": "OBT",
        "sourceInfo": {
            "sourcePnrId": "TXTDQG",
            "bookingSource": "SABRE",
            "thirdParty": "SABRE",
            "bookingDateTime": {"iso8601": "2025-05-07T16:15:52Z"},
            "posDescriptor": "1ZSK",
            "iataNumber": "",
        },
        "invoiceDelayedBooking": False,
        "travelers": [
            {
                "user": {
                    "dob": {"iso8601": "1989-12-30"},
                    "email": "<EMAIL>",
                    "gender": "MALE",
                    "name": {"family1": "Wang", "family2": "", "given": "Chengxuan", "middle": "", "preferred": ""},
                    "paymentInfos": [
                        {
                            "applicableTo": [],
                            "card": {
                                "id": "7004652e-291b-4347-83f6-a1d8ec52d34b",
                                "type": "CREDIT",
                                "company": "AMEX",
                                "name": "Chengxuan Wang",
                                "address": {
                                    "addressLines": ["3783 Monterey Ct N"],
                                    "postalCode": "98057",
                                    "regionCode": "US",
                                },
                                "number": "3XXXXXXXXXXX0005",
                                "expiryMonth": 1,
                                "expiryYear": 2030,
                                "cvv": "",
                                "label": "OTTO credit card",
                            },
                            "accessType": "PERSONAL",
                            "access": {
                                "accessType": "PERSONAL",
                                "entityIds": ["ff91d58d-60f7-45a0-ad2e-cbf26bb72676"],
                                "entities": [
                                    {
                                        "entityId": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676",
                                        "centralCardAccessLevel": "UNKNOWN",
                                    }
                                ],
                            },
                        }
                    ],
                    "phoneNumbers": [
                        {
                            "countryCode": 1,
                            "countryCodeSource": "UNSPECIFIED",
                            "extension": "",
                            "isoCountryCode": "US",
                            "italianLeadingZero": False,
                            "nationalNumber": 0,
                            "numberOfLeadingZeros": 0,
                            "preferredDomesticCarrierCode": "",
                            "rawInput": "2672419322",
                            "type": "MOBILE",
                        }
                    ],
                },
                "userBusinessInfo": {
                    "designation": "",
                    "email": "<EMAIL>",
                    "employeeId": "",
                    "legalEntityId": {"id": "96dd735a-d576-4e68-ad91-5e06da377e2e"},
                    "organizationId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                },
                "userOrgId": {
                    "organizationAgencyId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                    "organizationId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                    "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                    "tmcInfo": {
                        "id": {"id": "ecc5b835-8001-430c-98f8-fedeccebe4cf"},
                        "primaryServiceProviderTmc": {"tmcId": {"id": "ecc5b835-8001-430c-98f8-fedeccebe4cf"}},
                        "secondaryServiceProviderTmcs": [],
                        "partnerTmcId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                    },
                    "tmcBasicInfo": {
                        "contractingTmc": {"id": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}},
                        "bookingTmc": {"id": {"id": "ecc5b835-8001-430c-98f8-fedeccebe4cf"}},
                    },
                },
                "persona": "EMPLOYEE",
                "isActive": True,
                "tier": "BASIC",
            }
        ],
        "pnrTravelers": [
            {
                "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                "travelerInfo": {"userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"}, "adhocTravelerInfo": {}},
                "personalInfo": {
                    "dob": {"iso8601": "1989-12-30"},
                    "email": "<EMAIL>",
                    "gender": "MALE",
                    "name": {"family1": "Wang", "family2": "", "given": "Chengxuan", "middle": "", "preferred": ""},
                    "phoneNumbers": [
                        {
                            "countryCode": 1,
                            "countryCodeSource": "UNSPECIFIED",
                            "extension": "",
                            "isoCountryCode": "US",
                            "italianLeadingZero": False,
                            "nationalNumber": 0,
                            "numberOfLeadingZeros": 0,
                            "preferredDomesticCarrierCode": "",
                            "rawInput": "2672419322",
                            "type": "MOBILE",
                        }
                    ],
                },
                "loyalties": [],
                "persona": "EMPLOYEE",
                "businessInfo": {
                    "legalEntity": {
                        "id": "96dd735a-d576-4e68-ad91-5e06da377e2e",
                        "name": "Otto Trip, Inc.",
                        "ein": "",
                        "externalId": "",
                        "companySpecifiedAttributes": [],
                    },
                    "companyId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                    "companyInfo": {
                        "id": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                        "name": "Otto Trip",
                        "externalId": "",
                    },
                },
                "tier": "BASIC",
            }
        ],
        "costOfGoodsSold": {
            "payments": [
                {
                    "travelerIndices": [0],
                    "userIds": [{"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"}],
                    "fop": {
                        "type": "CARD",
                        "card": {
                            "id": "7004652e-291b-4347-83f6-a1d8ec52d34b",
                            "type": "CREDIT",
                            "company": "AMEX",
                            "name": "Chengxuan Wang",
                            "address": {
                                "addressLines": ["3783 Monterey Ct N"],
                                "administrativeArea": "WA",
                                "administrativeAreaName": "",
                                "description": "",
                                "isDefault": False,
                                "languageCode": "",
                                "locality": "Renton",
                                "locationCode": "",
                                "organization": "",
                                "postalCode": "98057",
                                "continentCode": "",
                                "recipients": [],
                                "regionCode": "US",
                                "regionName": "",
                                "revision": 0,
                                "sortingCode": "",
                                "sublocality": "",
                                "timezone": "",
                            },
                            "number": "3XXXXXXXXXXX0005",
                            "expiryMonth": 1,
                            "expiryYear": 2030,
                            "cvv": "",
                            "label": "OTTO credit card",
                            "currency": "",
                            "externalId": "",
                            "vaultId": "00000000-0000-0000-0000-000000000000",
                            "expiry": {},
                        },
                        "additionalInfo": "",
                        "accessType": {
                            "accessType": "PERSONAL",
                            "entityIds": ["ff91d58d-60f7-45a0-ad2e-cbf26bb72676"],
                            "entities": [
                                {
                                    "entityId": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676",
                                    "centralCardAccessLevel": "UNKNOWN",
                                }
                            ],
                        },
                        "paymentMethod": "CREDIT_CARD",
                        "paymentMetadata": {
                            "cardMetadata": {
                                "card": {
                                    "id": "7004652e-291b-4347-83f6-a1d8ec52d34b",
                                    "type": "CREDIT",
                                    "company": "AMEX",
                                    "name": "Chengxuan Wang",
                                    "address": {
                                        "addressLines": ["3783 Monterey Ct N"],
                                        "administrativeArea": "WA",
                                        "administrativeAreaName": "",
                                        "description": "",
                                        "isDefault": False,
                                        "languageCode": "",
                                        "locality": "Renton",
                                        "locationCode": "",
                                        "organization": "",
                                        "postalCode": "98057",
                                        "continentCode": "",
                                        "recipients": [],
                                        "regionCode": "US",
                                        "regionName": "",
                                        "revision": 0,
                                        "sortingCode": "",
                                        "sublocality": "",
                                        "timezone": "",
                                    },
                                    "number": "3XXXXXXXXXXX0005",
                                    "expiryMonth": 1,
                                    "expiryYear": 2030,
                                    "cvv": "",
                                    "label": "OTTO credit card",
                                    "currency": "",
                                    "externalId": "",
                                    "vaultId": "00000000-0000-0000-0000-000000000000",
                                    "expiry": {},
                                },
                                "accessType": {
                                    "accessType": "PERSONAL",
                                    "entityIds": ["ff91d58d-60f7-45a0-ad2e-cbf26bb72676"],
                                    "entities": [
                                        {
                                            "entityId": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676",
                                            "centralCardAccessLevel": "UNKNOWN",
                                        }
                                    ],
                                },
                                "isLodgeCard": False,
                            }
                        },
                        "paymentSourceType": "CARD",
                    },
                    "paymentReference": "",
                    "paymentType": "FLIGHTS",
                    "paymentThirdParty": "UNKNOWN_PARTY",
                    "paymentId": "",
                    "paymentGateway": "PAYMENT_GATEWAY_UNKNOWN",
                    "isRefunded": False,
                    "networkTransactionId": "",
                }
            ]
        },
        "transactions": [
            {
                "ctc": [
                    {
                        "isRefunded": False,
                        "amount": {
                            "amount": 153.6,
                            "currencyCode": "USD",
                            "convertedAmount": 153.6,
                            "convertedCurrency": "USD",
                        },
                        "fop": {
                            "type": "CARD",
                            "card": {
                                "id": "7004652e-291b-4347-83f6-a1d8ec52d34b",
                                "type": "CREDIT",
                                "company": "AMEX",
                                "name": "Chengxuan Wang",
                                "address": {
                                    "addressLines": ["3783 Monterey Ct N"],
                                    "postalCode": "98057",
                                    "regionCode": "US",
                                },
                                "number": "3XXXXXXXXXXX0005",
                                "expiryMonth": 1,
                                "expiryYear": 2030,
                                "cvv": "",
                                "label": "OTTO credit card",
                            },
                            "additionalInfo": "",
                            "paymentMethod": "CREDIT_CARD",
                        },
                        "isGuarantee": False,
                    }
                ],
                "itemGroups": [
                    {
                        "transactionType": "AIR_TICKET_ISSUED",
                        "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                        "confirmationNumber": "5267174579407",
                        "transactionDateTime": {"iso8601": "2025-05-07T16:16:42"},
                        "transactionAmountDiff": {
                            "base": {"amount": 0, "currencyCode": "", "convertedAmount": 0, "convertedCurrency": ""},
                            "tax": {"amount": 0, "currencyCode": "", "convertedAmount": 0, "convertedCurrency": ""},
                        },
                        "totalAmountDiff": {
                            "base": {"amount": 0, "currencyCode": "", "convertedAmount": 0, "convertedCurrency": ""},
                            "tax": {"amount": 0, "currencyCode": "", "convertedAmount": 0, "convertedCurrency": ""},
                        },
                        "transactionAmount": {
                            "base": {
                                "amount": 114.42,
                                "currencyCode": "USD",
                                "convertedAmount": 114.42,
                                "convertedCurrency": "USD",
                            },
                            "tax": {
                                "amount": 39.18,
                                "currencyCode": "USD",
                                "convertedAmount": 39.18,
                                "convertedCurrency": "USD",
                            },
                        },
                        "totalAmount": {
                            "base": {
                                "amount": 114.42,
                                "currencyCode": "USD",
                                "convertedAmount": 114.42,
                                "convertedCurrency": "USD",
                            },
                            "tax": {
                                "amount": 39.18,
                                "currencyCode": "USD",
                                "convertedAmount": 39.18,
                                "convertedCurrency": "USD",
                            },
                        },
                        "invoiceData": {
                            "invoiceNumber": "SPOT-US-002094",
                            "buyer": {
                                "name": "Chengxuan Wang",
                                "address": "Otto Trip, Inc.,\n11 Brooke Dr,\nNovato,\nCA US 94947",
                                "idInfo": [],
                            },
                            "seller": {
                                "name": "test",
                                "address": "test,\ndfgasgsaasf,\ndfsadadf US asdfdsa",
                                "idInfo": [{"idType": "EIN", "value": "3454"}],
                            },
                        },
                        "items": [
                            {
                                "itemType": "AIR_ITEM",
                                "airItemType": "FLIGHT",
                                "flights": [
                                    {
                                        "arrivalDateTime": {"iso8601": "2025-06-12T21:40:00"},
                                        "departureDateTime": {"iso8601": "2025-06-12T19:30:00"},
                                        "marketing": {"num": "1187", "airlineCode": "WN"},
                                        "operating": {"num": "1187", "airlineCode": "WN"},
                                        "origin": {
                                            "airportCode": "SEA",
                                            "airportName": "Seattle–Tacoma International Airport",
                                            "cityCode": "",
                                        },
                                        "destination": {
                                            "airportCode": "OAK",
                                            "airportName": "Oakland International Airport",
                                            "cityCode": "",
                                        },
                                    },
                                    {
                                        "arrivalDateTime": {"iso8601": "2025-06-14T21:40:00"},
                                        "departureDateTime": {"iso8601": "2025-06-14T19:35:00"},
                                        "marketing": {"num": "383", "airlineCode": "WN"},
                                        "operating": {"num": "383", "airlineCode": "WN"},
                                        "origin": {
                                            "airportCode": "OAK",
                                            "airportName": "Oakland International Airport",
                                            "cityCode": "",
                                        },
                                        "destination": {
                                            "airportCode": "SEA",
                                            "airportName": "Seattle–Tacoma International Airport",
                                            "cityCode": "",
                                        },
                                    },
                                ],
                                "ancillaryTypes": [],
                                "oldFlights": [],
                            }
                        ],
                    }
                ],
                "pnrVersion": 7,
            },
            {
                "ctc": [
                    {
                        "isRefunded": False,
                        "amount": {
                            "amount": 9.7,
                            "currencyCode": "USD",
                            "convertedAmount": 9.7,
                            "convertedCurrency": "USD",
                        },
                        "fop": {
                            "type": "CARD",
                            "card": {
                                "id": "7004652e-291b-4347-83f6-a1d8ec52d34b",
                                "type": "CREDIT",
                                "company": "AMEX",
                                "name": "Chengxuan Wang",
                                "address": {
                                    "addressLines": ["3783 Monterey Ct N"],
                                    "postalCode": "98057",
                                    "regionCode": "US",
                                },
                                "number": "3XXXXXXXXXXX0005",
                                "expiryMonth": 1,
                                "expiryYear": 2030,
                                "cvv": "",
                                "label": "OTTO credit card",
                            },
                            "additionalInfo": "",
                            "paymentMethod": "CREDIT_CARD",
                        },
                        "isGuarantee": False,
                    }
                ],
                "itemGroups": [
                    {
                        "transactionType": "AIR_TICKET_EXCHANGED",
                        "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                        "confirmationNumber": "5267174579427",
                        "transactionDateTime": {"iso8601": "2025-05-07T17:49:18"},
                        "transactionAmountDiff": {
                            "base": {
                                "amount": 0,
                                "currencyCode": "USD",
                                "convertedAmount": 0,
                                "convertedCurrency": "USD",
                            },
                            "tax": {
                                "amount": 9.7,
                                "currencyCode": "USD",
                                "convertedAmount": 9.7,
                                "convertedCurrency": "USD",
                            },
                        },
                        "totalAmountDiff": {
                            "base": {
                                "amount": 0,
                                "currencyCode": "USD",
                                "convertedAmount": 0,
                                "convertedCurrency": "USD",
                            },
                            "tax": {
                                "amount": 9.7,
                                "currencyCode": "USD",
                                "convertedAmount": 9.7,
                                "convertedCurrency": "USD",
                            },
                        },
                        "penaltyDiff": {
                            "amount": 0,
                            "currencyCode": "USD",
                            "convertedAmount": 0,
                            "convertedCurrency": "USD",
                        },
                        "transactionAmount": {
                            "base": {
                                "amount": 114.42,
                                "currencyCode": "USD",
                                "convertedAmount": 114.42,
                                "convertedCurrency": "USD",
                            },
                            "tax": {
                                "amount": 48.88,
                                "currencyCode": "USD",
                                "convertedAmount": 48.88,
                                "convertedCurrency": "USD",
                            },
                        },
                        "totalAmount": {
                            "base": {
                                "amount": 114.42,
                                "currencyCode": "USD",
                                "convertedAmount": 114.42,
                                "convertedCurrency": "USD",
                            },
                            "tax": {
                                "amount": 48.88,
                                "currencyCode": "USD",
                                "convertedAmount": 48.88,
                                "convertedCurrency": "USD",
                            },
                        },
                        "penalty": {
                            "amount": 0,
                            "currencyCode": "USD",
                            "convertedAmount": 0,
                            "convertedCurrency": "USD",
                        },
                        "originalTicketNumber": "5267174579407",
                        "invoiceData": {
                            "invoiceNumber": "SPOT-US-002097",
                            "buyer": {
                                "name": "Chengxuan Wang",
                                "address": "Otto Trip, Inc.,\n11 Brooke Dr,\nNovato,\nCA US 94947",
                                "idInfo": [],
                            },
                            "seller": {
                                "name": "test",
                                "address": "test,\ndfgasgsaasf,\ndfsadadf US asdfdsa",
                                "idInfo": [{"idType": "EIN", "value": "3454"}],
                            },
                        },
                        "items": [
                            {
                                "itemType": "AIR_ITEM",
                                "airItemType": "FLIGHT",
                                "flights": [
                                    {
                                        "arrivalDateTime": {"iso8601": "2025-06-12T07:20:00"},
                                        "departureDateTime": {"iso8601": "2025-06-12T05:15:00"},
                                        "marketing": {"num": "4069", "airlineCode": "WN"},
                                        "operating": {"num": "4069", "airlineCode": "WN"},
                                        "origin": {
                                            "airportCode": "SEA",
                                            "airportName": "Seattle–Tacoma International Airport",
                                            "cityCode": "",
                                        },
                                        "destination": {
                                            "airportCode": "OAK",
                                            "airportName": "Oakland International Airport",
                                            "cityCode": "",
                                        },
                                    },
                                    {
                                        "arrivalDateTime": {"iso8601": "2025-06-14T18:40:00"},
                                        "departureDateTime": {"iso8601": "2025-06-14T17:05:00"},
                                        "marketing": {"num": "4040", "airlineCode": "WN"},
                                        "operating": {"num": "4040", "airlineCode": "WN"},
                                        "origin": {
                                            "airportCode": "OAK",
                                            "airportName": "Oakland International Airport",
                                            "cityCode": "",
                                        },
                                        "destination": {
                                            "airportCode": "LAS",
                                            "airportName": "Harry Reid International Airport",
                                            "cityCode": "",
                                        },
                                    },
                                    {
                                        "arrivalDateTime": {"iso8601": "2025-06-15T00:30:00"},
                                        "departureDateTime": {"iso8601": "2025-06-14T21:55:00"},
                                        "marketing": {"num": "2830", "airlineCode": "WN"},
                                        "operating": {"num": "2830", "airlineCode": "WN"},
                                        "origin": {
                                            "airportCode": "LAS",
                                            "airportName": "Harry Reid International Airport",
                                            "cityCode": "",
                                        },
                                        "destination": {
                                            "airportCode": "SEA",
                                            "airportName": "Seattle–Tacoma International Airport",
                                            "cityCode": "",
                                        },
                                    },
                                ],
                                "ancillaryTypes": [],
                                "oldFlights": [],
                            },
                            {
                                "itemType": "AIR_ITEM",
                                "airItemType": "FLIGHT",
                                "flights": [],
                                "ancillaryTypes": [],
                                "oldFlights": [
                                    {
                                        "arrivalDateTime": {"iso8601": "2025-06-12T21:40:00"},
                                        "departureDateTime": {"iso8601": "2025-06-12T19:30:00"},
                                        "marketing": {"num": "1187", "airlineCode": "WN"},
                                        "operating": {"num": "1187", "airlineCode": "WN"},
                                        "origin": {
                                            "airportCode": "SEA",
                                            "airportName": "Seattle–Tacoma International Airport",
                                            "cityCode": "",
                                        },
                                        "destination": {
                                            "airportCode": "OAK",
                                            "airportName": "Oakland International Airport",
                                            "cityCode": "",
                                        },
                                    },
                                    {
                                        "arrivalDateTime": {"iso8601": "2025-06-14T21:40:00"},
                                        "departureDateTime": {"iso8601": "2025-06-14T19:35:00"},
                                        "marketing": {"num": "383", "airlineCode": "WN"},
                                        "operating": {"num": "383", "airlineCode": "WN"},
                                        "origin": {
                                            "airportCode": "OAK",
                                            "airportName": "Oakland International Airport",
                                            "cityCode": "",
                                        },
                                        "destination": {
                                            "airportCode": "SEA",
                                            "airportName": "Seattle–Tacoma International Airport",
                                            "cityCode": "",
                                        },
                                    },
                                ],
                            },
                        ],
                    }
                ],
                "pnrVersion": 14,
            },
        ],
        "isFinalized": True,
        "policyInfo": {
            "outOfPolicy": False,
            "reasonCode": "UNKNOWN_CHECKOUT_ANSWER_TYPE",
            "reason": "",
            "appliedPolicyInfo": {
                "policies": [{"id": "366011aa-9ad6-4cab-bb6b-add9ef060c33", "version": "0"}],
                "ruleResultInfos": [],
            },
        },
        "airPnr": {
            "legs": [
                {
                    "flights": [
                        {
                            "departureDateTime": {"iso8601": "2025-06-12T05:15:00"},
                            "arrivalDateTime": {"iso8601": "2025-06-12T07:20:00"},
                            "duration": {"iso8601": "PT2H5M"},
                            "flightId": "CgNTRUESA09BSxoVChMyMDI1LTA2LTEyVDA1OjE1OjAwIhUKEzIwMjUtMDYtMTJUMDc6MjA6MDA=",
                            "origin": "SEA",
                            "destination": "OAK",
                            "departureGate": {"gate": "", "terminal": ""},
                            "arrivalGate": {"gate": "2", "terminal": "TERMINAL 2 LIONEL WILSON"},
                            "marketing": {"num": "4069", "airlineCode": "WN"},
                            "operating": {"num": "4069", "airlineCode": "WN"},
                            "operatingAirlineName": "",
                            "hiddenStops": [],
                            "vendorConfirmationNumber": "BR6XQX",
                            "cabin": "ECONOMY",
                            "bookingCode": "D",
                            "flightStatus": "CONFIRMED",
                            "otherStatuses": [],
                            "co2EmissionDetail": {
                                "emissionValue": 0.160776,
                                "averageEmissionValue": 0.1827,
                                "flightDistanceKm": 1180,
                                "isApproximate": False,
                            },
                            "restrictions": [],
                            "sourceStatus": "HK",
                            "equipment": {"code": "73H", "type": "", "name": "Boeing 737-800 Winglets"},
                            "distance": {"length": 670, "unit": "MILE"},
                            "flightWaiverCodes": [],
                            "amenities": [{}, {}, {}, {}, {}, {}, {}, {}],
                            "flightIndex": 0,
                        }
                    ],
                    "brandName": "Wanna Get Away",
                    "validatingAirlineCode": "WN",
                    "legStatus": "CONFIRMED_STATUS",
                    "sortingPriority": 0,
                    "travelerRestrictions": [],
                    "fareOffers": [
                        {
                            "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                            "baggagePolicy": {
                                "checkedIn": [
                                    {"description": "2 checked bags, 50 lbs each"},
                                    {"description": "1 checked bag, 100 lbs (350 USD)"},
                                ],
                                "carryOn": [{"description": "1 carry-on bag"}],
                            },
                        },
                        {
                            "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                            "cancellationPolicy": {"description": "Non-refundable"},
                        },
                        {
                            "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                            "exchangePolicy": {"description": "Change allowed for free"},
                        },
                    ],
                    "legId": "CgNTRUESA09BSxoKNjY2OTk4MjI0Mg==",
                    "rateType": "PUBLISHED",
                    "preferredTypes": [],
                    "preferences": [],
                    "legIndex": 0,
                },
                {
                    "flights": [
                        {
                            "departureDateTime": {"iso8601": "2025-06-14T17:05:00"},
                            "arrivalDateTime": {"iso8601": "2025-06-14T18:40:00"},
                            "duration": {"iso8601": "PT1H35M"},
                            "flightId": "CgNPQUsSA0xBUxoVChMyMDI1LTA2LTE0VDE3OjA1OjAwIhUKEzIwMjUtMDYtMTRUMTg6NDA6MDA=",
                            "origin": "OAK",
                            "destination": "LAS",
                            "departureGate": {"gate": "", "terminal": "TERMINAL 2 LIONEL WILSON"},
                            "arrivalGate": {"gate": "1", "terminal": "TERMINAL 1"},
                            "marketing": {"num": "4040", "airlineCode": "WN"},
                            "operating": {"num": "4040", "airlineCode": "WN"},
                            "operatingAirlineName": "",
                            "hiddenStops": [],
                            "vendorConfirmationNumber": "BR6XQX",
                            "cabin": "ECONOMY",
                            "bookingCode": "V",
                            "flightStatus": "CONFIRMED",
                            "otherStatuses": [],
                            "co2EmissionDetail": {
                                "emissionValue": 0.10604,
                                "averageEmissionValue": 0.1205,
                                "flightDistanceKm": 753,
                                "isApproximate": False,
                            },
                            "restrictions": [],
                            "sourceStatus": "HK",
                            "equipment": {"code": "73H", "type": "", "name": "Boeing 737-800 Winglets"},
                            "distance": {"length": 407, "unit": "MILE"},
                            "flightWaiverCodes": [],
                            "amenities": [{}, {}, {}, {}, {}, {}, {}, {}],
                            "flightIndex": 0,
                        },
                        {
                            "departureDateTime": {"iso8601": "2025-06-14T21:55:00"},
                            "arrivalDateTime": {"iso8601": "2025-06-15T00:30:00"},
                            "duration": {"iso8601": "PT2H35M"},
                            "flightId": "CgNMQVMSA1NFQRoVChMyMDI1LTA2LTE0VDIxOjU1OjAwIhUKEzIwMjUtMDYtMTVUMDA6MzA6MDA=",
                            "origin": "LAS",
                            "destination": "SEA",
                            "departureGate": {"gate": "", "terminal": "TERMINAL 1"},
                            "arrivalGate": {"gate": "", "terminal": ""},
                            "marketing": {"num": "2830", "airlineCode": "WN"},
                            "operating": {"num": "2830", "airlineCode": "WN"},
                            "operatingAirlineName": "",
                            "hiddenStops": [],
                            "vendorConfirmationNumber": "BR6XQX",
                            "cabin": "ECONOMY",
                            "bookingCode": "V",
                            "flightStatus": "CONFIRMED",
                            "otherStatuses": [],
                            "co2EmissionDetail": {
                                "emissionValue": 0.200816,
                                "averageEmissionValue": 0.2282,
                                "flightDistanceKm": 1494,
                                "isApproximate": False,
                            },
                            "restrictions": [],
                            "sourceStatus": "HK",
                            "equipment": {"code": "73H", "type": "", "name": "Boeing 737-800 Winglets"},
                            "distance": {"length": 866, "unit": "MILE"},
                            "flightWaiverCodes": [],
                            "amenities": [],
                            "flightIndex": 1,
                        },
                    ],
                    "brandName": "Wanna Get Away",
                    "validatingAirlineCode": "WN",
                    "legStatus": "CONFIRMED_STATUS",
                    "sortingPriority": 0,
                    "travelerRestrictions": [],
                    "fareOffers": [
                        {
                            "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                            "baggagePolicy": {
                                "checkedIn": [
                                    {"description": "2 checked bags, 50 lbs each"},
                                    {"description": "1 checked bag, 100 lbs (350 USD)"},
                                ],
                                "carryOn": [{"description": "1 carry-on bag"}],
                            },
                        },
                        {
                            "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                            "cancellationPolicy": {"description": "Non-refundable"},
                        },
                        {
                            "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                            "exchangePolicy": {"description": "Change allowed for free"},
                        },
                    ],
                    "legId": "CgNPQUsSA1NFQRoKNjY2OTk4MjI0Mg==",
                    "rateType": "PUBLISHED",
                    "preferredTypes": [],
                    "preferences": [],
                    "legIndex": 1,
                },
            ],
            "airPnrRemarks": [
                {"remarkString": "OTTO TRIP, INC."},
                {"remarkString": "AUTH-AMEX/AX0005/07MAY/01541746640139266686       "},
                {"remarkString": "HTL-RATECODE-THR"},
                {"remarkString": "TRIPFEE-BC0.00/EXCX0.00"},
                {"remarkString": "ISPASSIVEPNR FALSE"},
                {"remarkString": "  AUTH-AVS BILLING ADDRESS AND ZIP POSTAL ARE MATC"},
                {"remarkString": "PNRID 6669982242"},
                {"remarkString": "S*UD78 IN POLICY"},
                {"remarkString": "*AX3XXXXXXXXXX0005¥01/30-XN"},
                {"remarkString": "BOOKEDBYORGID 4ECABB34-5EB3-4192-A1C8-C634A151DC41"},
                {"remarkString": "2-SABREPROFILES¥OTTO TRIP INC."},
                {"remarkString": "S*HU"},
                {"remarkString": "PHONESPOTNANA"},
                {"remarkString": "AEBT/5267174579427/1ZSK*AWS/1ZSK*AWS"},
                {"remarkString": "MAILBOXSPOTNANA"},
                {"remarkString": "CURR-USD"},
                {"remarkString": "AUTH-AMEX/AX0005/07MAY/01261746634587760646       "},
                {"remarkString": "S*UD166 SPOTNANA"},
                {"remarkString": "11 BROOKE DR"},
                {"remarkString": "HTL-RATECODE-WTH"},
                {"remarkString": "TRACEID 07C30C6BF2A35E58"},
                {"remarkString": "TRAVELERPID FF91D58D-60F7-45A0-AD2E-CBF26BB72676"},
                {"remarkString": "WORKFLOWID CE9695E3A9DB4628"},
                {"remarkString": "BOOKEDBYUSERID 2A8658AD-0572-480C-BBC0-B8BD3B3FB78C"},
                {"remarkString": "TRAVELERORGID 4ECABB34-5EB3-4192-A1C8-C634A151DC41"},
                {"remarkString": "TRIPID 5148867556"},
                {"remarkString": "S*UD212 OTTO TRIP INC."},
                {"remarkString": "NOVATO CA US 94947"},
                {"remarkString": "S*ICSPOTNANA"},
                {"remarkString": "PNRTYPE AIR"},
                {"remarkString": "XXTAW/"},
                {"remarkString": "ENVIRONMENT SBOXMETA"},
                {"remarkString": "AA-TCC859996"},
                {"remarkString": "  AUTH-APV/Y84173/000/USD9.70                     "},
                {"remarkString": "HTL-RATECODE-SIG"},
                {"remarkString": "S*SA804"},
                {"remarkString": "XXAUTH/Y59814/AX3XXXXXXXXXX0005/WN/USD153.60/07MAY/S"},
                {"remarkString": "BA-TCC859996"},
                {"remarkString": "HTL-RATECODE-ABC"},
                {"remarkString": "HTL-RATECODE-FHD"},
                {"remarkString": "HTL-RATECODE-PP6"},
                {"remarkString": "XXAUTH/Y84173/AX3XXXXXXXXXX0005/WN/USD9.70/07MAY/S"},
                {"remarkString": "PPT DOB-12/30/1989 WANG/CHENGXUAN -M"},
                {"remarkString": "  AUTH-APV/Y59814/000/USD153.60                   "},
                {"remarkString": "NO EMAIL"},
                {"remarkString": "S*UD3 5148867556"},
                {"remarkString": "HTL-RATECODE-FHP"},
                {"remarkString": "AIR-SEQ1"},
                {"remarkString": "  AUTH-CSC MATCHED/Y                              "},
                {"remarkString": "IB-TCC859996"},
            ],
            "travelerInfos": [
                {
                    "airVendorCancellationInfo": {"airVendorCancellationObjects": []},
                    "createdMcos": [],
                    "travelerIdx": 0,
                    "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                    "paxType": "ADULT",
                    "tickets": [
                        {
                            "ticketNumber": "5267174579407",
                            "ticketType": "FLIGHT",
                            "issuedDateTime": {"iso8601": "2025-05-07T11:16:00"},
                            "status": "EXCHANGED",
                            "amount": {
                                "base": {
                                    "amount": 114.42,
                                    "currencyCode": "USD",
                                    "convertedAmount": 114.42,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "tax": {
                                    "amount": 39.18,
                                    "currencyCode": "USD",
                                    "convertedAmount": 39.18,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                            },
                            "flightCoupons": [],
                            "ancillaries": [],
                            "validatingAirlineCode": "WN",
                            "exchangePolicy": {
                                "exchangePenalty": {
                                    "amount": 0,
                                    "currencyCode": "USD",
                                    "convertedAmount": 0,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "isExchangeable": True,
                                "isCat16": False,
                                "isConditional": False,
                            },
                            "refundPolicy": {
                                "isRefundable": False,
                                "isRefundableByObt": False,
                                "isCat16": False,
                                "isConditional": False,
                            },
                            "refundInfo": {
                                "refundAmount": {
                                    "base": {
                                        "amount": 0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "tax": {
                                        "amount": 0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                }
                            },
                            "taxBreakdown": {
                                "tax": [
                                    {
                                        "amount": {
                                            "amount": 10.4,
                                            "currencyCode": "USD",
                                            "convertedAmount": 10.4,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "taxCode": "ZP",
                                    },
                                    {
                                        "amount": {
                                            "amount": 11.2,
                                            "currencyCode": "USD",
                                            "convertedAmount": 11.2,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "taxCode": "AY",
                                    },
                                    {
                                        "amount": {
                                            "amount": 8.58,
                                            "currencyCode": "USD",
                                            "convertedAmount": 8.58,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "taxCode": "US",
                                    },
                                    {
                                        "amount": {
                                            "amount": 9,
                                            "currencyCode": "USD",
                                            "convertedAmount": 9,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "taxCode": "XF",
                                    },
                                ]
                            },
                            "commission": {
                                "amount": {
                                    "amount": 0,
                                    "currencyCode": "USD",
                                    "convertedAmount": 0,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "percent": 0,
                            },
                            "iataNumber": "14640990",
                            "fareCalculation": "SEA WN OAK67.91WN SEA46.51USD114.42END ZPSEAOAK XFSEA4.5OAK4.5",
                            "updateDateTime": {"iso8601": "2025-05-07T12:48:00"},
                            "paymentDetails": [
                                {
                                    "amount": {
                                        "base": {
                                            "amount": 114.42,
                                            "currencyCode": "USD",
                                            "convertedAmount": 114.42,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "tax": {
                                            "amount": 39.18,
                                            "currencyCode": "USD",
                                            "convertedAmount": 39.18,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                    },
                                    "fop": {
                                        "type": "CARD",
                                        "card": {
                                            "id": "7004652e-291b-4347-83f6-a1d8ec52d34b",
                                            "type": "CREDIT",
                                            "company": "AMEX",
                                            "name": "Chengxuan Wang",
                                            "address": {
                                                "addressLines": ["3783 Monterey Ct N"],
                                                "administrativeArea": "",
                                                "administrativeAreaName": "",
                                                "description": "",
                                                "isDefault": False,
                                                "languageCode": "",
                                                "locality": "",
                                                "locationCode": "",
                                                "organization": "",
                                                "postalCode": "98057",
                                                "continentCode": "",
                                                "recipients": [],
                                                "regionCode": "US",
                                                "regionName": "",
                                                "revision": 0,
                                                "sortingCode": "",
                                                "sublocality": "",
                                                "timezone": "",
                                            },
                                            "number": "3XXXXXXXXXXX0005",
                                            "expiryMonth": 1,
                                            "expiryYear": 2030,
                                            "cvv": "",
                                            "label": "OTTO credit card",
                                            "currency": "",
                                            "externalId": "",
                                        },
                                        "additionalInfo": "",
                                        "accessType": {
                                            "accessType": "PERSONAL",
                                            "entityIds": ["ff91d58d-60f7-45a0-ad2e-cbf26bb72676"],
                                            "entities": [
                                                {
                                                    "entityId": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676",
                                                    "centralCardAccessLevel": "UNKNOWN",
                                                }
                                            ],
                                        },
                                        "paymentMethod": "CREDIT_CARD",
                                    },
                                    "isRefunded": False,
                                }
                            ],
                            "ticketSettlement": "ARC_TICKET",
                            "publishedFare": {
                                "base": {
                                    "amount": 114.42,
                                    "currencyCode": "USD",
                                    "convertedAmount": 114.42,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "tax": {
                                    "amount": 39.18,
                                    "currencyCode": "USD",
                                    "convertedAmount": 39.18,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                            },
                            "vendorCancellationId": "",
                            "conjunctionTicketSuffix": [],
                            "pcc": "1ZSK",
                            "ticketIncompleteReasons": [],
                        },
                        {
                            "ticketNumber": "5267174579427",
                            "ticketType": "FLIGHT",
                            "issuedDateTime": {"iso8601": "2025-05-07T12:48:00"},
                            "status": "ISSUED",
                            "amount": {
                                "base": {
                                    "amount": 114.42,
                                    "currencyCode": "USD",
                                    "convertedAmount": 114.42,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "tax": {
                                    "amount": 48.88,
                                    "currencyCode": "USD",
                                    "convertedAmount": 48.88,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                            },
                            "flightCoupons": [
                                {"legIdx": 0, "flightIdx": 0, "status": "NOT_FLOWN"},
                                {"legIdx": 1, "flightIdx": 0, "status": "NOT_FLOWN"},
                                {"legIdx": 1, "flightIdx": 1, "status": "NOT_FLOWN"},
                            ],
                            "ancillaries": [],
                            "validatingAirlineCode": "WN",
                            "exchangePolicy": {
                                "exchangePenalty": {
                                    "amount": 0,
                                    "currencyCode": "USD",
                                    "convertedAmount": 0,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "isExchangeable": True,
                                "isCat16": False,
                                "isConditional": False,
                            },
                            "exchangeInfo": {
                                "originalTicketNumber": "5267174579407",
                                "addCollectAmount": {
                                    "base": {
                                        "amount": 0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "tax": {
                                        "amount": 9.7,
                                        "currencyCode": "USD",
                                        "convertedAmount": 9.7,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                },
                                "originalTicketDetails": {
                                    "ticketNumber": "5267174579407",
                                    "amount": {
                                        "base": {
                                            "amount": 114.42,
                                            "currencyCode": "USD",
                                            "convertedAmount": 114.42,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "tax": {
                                            "amount": 39.18,
                                            "currencyCode": "USD",
                                            "convertedAmount": 39.18,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                    },
                                    "taxBreakdown": {
                                        "tax": [
                                            {
                                                "amount": {
                                                    "amount": 10.4,
                                                    "currencyCode": "USD",
                                                    "convertedAmount": 10.4,
                                                    "convertedCurrency": "USD",
                                                    "otherCoinage": [],
                                                },
                                                "taxCode": "ZP",
                                            },
                                            {
                                                "amount": {
                                                    "amount": 11.2,
                                                    "currencyCode": "USD",
                                                    "convertedAmount": 11.2,
                                                    "convertedCurrency": "USD",
                                                    "otherCoinage": [],
                                                },
                                                "taxCode": "AY",
                                            },
                                            {
                                                "amount": {
                                                    "amount": 8.58,
                                                    "currencyCode": "USD",
                                                    "convertedAmount": 8.58,
                                                    "convertedCurrency": "USD",
                                                    "otherCoinage": [],
                                                },
                                                "taxCode": "US",
                                            },
                                            {
                                                "amount": {
                                                    "amount": 9,
                                                    "currencyCode": "USD",
                                                    "convertedAmount": 9,
                                                    "convertedCurrency": "USD",
                                                    "otherCoinage": [],
                                                },
                                                "taxCode": "XF",
                                            },
                                        ]
                                    },
                                    "conjunctionTicketSuffix": "",
                                    "ctcRefund": {
                                        "base": {
                                            "amount": 0,
                                            "currencyCode": "USD",
                                            "convertedAmount": 0,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "tax": {
                                            "amount": 0,
                                            "currencyCode": "USD",
                                            "convertedAmount": 0,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                    },
                                },
                            },
                            "refundPolicy": {
                                "isRefundable": False,
                                "isRefundableByObt": False,
                                "isCat16": False,
                                "isConditional": False,
                            },
                            "taxBreakdown": {
                                "tax": [
                                    {
                                        "amount": {
                                            "amount": 15.6,
                                            "currencyCode": "USD",
                                            "convertedAmount": 15.6,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "taxCode": "ZP",
                                    },
                                    {
                                        "amount": {
                                            "amount": 11.2,
                                            "currencyCode": "USD",
                                            "convertedAmount": 11.2,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "taxCode": "AY",
                                    },
                                    {
                                        "amount": {
                                            "amount": 8.58,
                                            "currencyCode": "USD",
                                            "convertedAmount": 8.58,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "taxCode": "US",
                                    },
                                    {
                                        "amount": {
                                            "amount": 13.5,
                                            "currencyCode": "USD",
                                            "convertedAmount": 13.5,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "taxCode": "XF",
                                    },
                                ]
                            },
                            "commission": {
                                "amount": {
                                    "amount": 0,
                                    "currencyCode": "USD",
                                    "convertedAmount": 0,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "percent": 0,
                            },
                            "iataNumber": "14640990",
                            "fareCalculation": "SEA WN OAK67.91WN X/LAS WN SEA46.51USD114.42END ZPSEAOAKLAS XFSEA4.5OAK4.5LAS4.5",
                            "paymentDetails": [
                                {
                                    "amount": {
                                        "base": {
                                            "amount": 0,
                                            "currencyCode": "USD",
                                            "convertedAmount": 0,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "tax": {
                                            "amount": 9.7,
                                            "currencyCode": "USD",
                                            "convertedAmount": 9.7,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                    },
                                    "fop": {
                                        "type": "CARD",
                                        "card": {
                                            "id": "7004652e-291b-4347-83f6-a1d8ec52d34b",
                                            "type": "CREDIT",
                                            "company": "AMEX",
                                            "name": "Chengxuan Wang",
                                            "address": {
                                                "addressLines": ["3783 Monterey Ct N"],
                                                "administrativeArea": "",
                                                "administrativeAreaName": "",
                                                "description": "",
                                                "isDefault": False,
                                                "languageCode": "",
                                                "locality": "",
                                                "locationCode": "",
                                                "organization": "",
                                                "postalCode": "98057",
                                                "continentCode": "",
                                                "recipients": [],
                                                "regionCode": "US",
                                                "regionName": "",
                                                "revision": 0,
                                                "sortingCode": "",
                                                "sublocality": "",
                                                "timezone": "",
                                            },
                                            "number": "3XXXXXXXXXXX0005",
                                            "expiryMonth": 1,
                                            "expiryYear": 2030,
                                            "cvv": "",
                                            "label": "OTTO credit card",
                                            "currency": "",
                                            "externalId": "",
                                        },
                                        "additionalInfo": "",
                                        "accessType": {
                                            "accessType": "PERSONAL",
                                            "entityIds": ["ff91d58d-60f7-45a0-ad2e-cbf26bb72676"],
                                            "entities": [
                                                {
                                                    "entityId": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676",
                                                    "centralCardAccessLevel": "UNKNOWN",
                                                }
                                            ],
                                        },
                                        "paymentMethod": "CREDIT_CARD",
                                    },
                                    "isRefunded": False,
                                }
                            ],
                            "ticketSettlement": "ARC_TICKET",
                            "publishedFare": {
                                "base": {
                                    "amount": 114.42,
                                    "currencyCode": "USD",
                                    "convertedAmount": 114.42,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "tax": {
                                    "amount": 48.88,
                                    "currencyCode": "USD",
                                    "convertedAmount": 48.88,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                            },
                            "vendorCancellationId": "",
                            "conjunctionTicketSuffix": [],
                            "pcc": "1ZSK",
                            "ticketIncompleteReasons": [],
                        },
                    ],
                    "boardingPass": [],
                    "booking": {
                        "seats": [],
                        "luggageDetails": [],
                        "otherAncillaries": [],
                        "itinerary": {
                            "totalFare": {
                                "base": {
                                    "amount": 114.42,
                                    "currencyCode": "USD",
                                    "convertedAmount": 114.42,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "tax": {
                                    "amount": 48.88,
                                    "currencyCode": "USD",
                                    "convertedAmount": 48.88,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                            },
                            "totalFlightsFare": {
                                "base": {
                                    "amount": 114.42,
                                    "currencyCode": "USD",
                                    "convertedAmount": 114.42,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "tax": {
                                    "amount": 48.88,
                                    "currencyCode": "USD",
                                    "convertedAmount": 48.88,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                            },
                            "flightFareBreakup": [
                                {
                                    "legIndices": [0, 1],
                                    "flightsFare": {
                                        "base": {
                                            "amount": 114.42,
                                            "currencyCode": "USD",
                                            "convertedAmount": 114.42,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "tax": {
                                            "amount": 48.88,
                                            "currencyCode": "USD",
                                            "convertedAmount": 48.88,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                    },
                                }
                            ],
                            "fareComponents": [
                                {
                                    "fareBasisCode": "DLNUW2H",
                                    "tourCode": "",
                                    "ticketDesignator": "",
                                    "baseFare": {
                                        "amount": 67.91,
                                        "currencyCode": "USD",
                                        "convertedAmount": 67.91,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "flightIds": [{"legIdx": 0, "flightIdx": 0}],
                                },
                                {
                                    "fareBasisCode": "VLAVW2H",
                                    "tourCode": "",
                                    "ticketDesignator": "",
                                    "baseFare": {
                                        "amount": 46.51,
                                        "currencyCode": "USD",
                                        "convertedAmount": 46.51,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "flightIds": [{"legIdx": 1, "flightIdx": 0}, {"legIdx": 1, "flightIdx": 1}],
                                },
                            ],
                            "otherAncillaryFares": [],
                        },
                        "otherCharges": [],
                    },
                    "appliedCredits": [],
                    "specialServiceRequestInfos": [],
                }
            ],
            "automatedCancellationInfo": {
                "supportedCancellations": [
                    {
                        "cancelType": "VOID",
                        "maxCancellationDateTime": {"iso8601": "2025-05-08T04:59:00Z"},
                        "totalFare": {
                            "base": {
                                "amount": 114.42,
                                "currencyCode": "USD",
                                "convertedAmount": 114.42,
                                "convertedCurrency": "USD",
                                "otherCoinage": [],
                            },
                            "tax": {
                                "amount": 48.88,
                                "currencyCode": "USD",
                                "convertedAmount": 48.88,
                                "convertedCurrency": "USD",
                                "otherCoinage": [],
                            },
                        },
                        "refund": {
                            "amount": 163.3,
                            "currencyCode": "USD",
                            "convertedAmount": 163.3,
                            "convertedCurrency": "USD",
                            "otherCoinage": [],
                        },
                    }
                ]
            },
            "bookingMetadata": {
                "fareStatistics": {
                    "statisticsItems": [
                        {
                            "statisticType": "MINIMUM",
                            "totalFare": {
                                "base": {
                                    "amount": 93.02,
                                    "currencyCode": "USD",
                                    "convertedAmount": 93.02,
                                    "convertedCurrency": "USD",
                                },
                                "tax": {
                                    "amount": 52.48,
                                    "currencyCode": "USD",
                                    "convertedAmount": 52.48,
                                    "convertedCurrency": "USD",
                                },
                            },
                        },
                        {
                            "statisticType": "MEDIAN",
                            "totalFare": {
                                "base": {
                                    "amount": 140.46,
                                    "currencyCode": "USD",
                                    "convertedAmount": 140.46,
                                    "convertedCurrency": "USD",
                                },
                                "tax": {
                                    "amount": 50.84,
                                    "currencyCode": "USD",
                                    "convertedAmount": 50.84,
                                    "convertedCurrency": "USD",
                                },
                            },
                        },
                        {
                            "statisticType": "MAXIMUM",
                            "totalFare": {
                                "base": {
                                    "amount": 3418.18,
                                    "currencyCode": "USD",
                                    "convertedAmount": 3418.18,
                                    "convertedCurrency": "USD",
                                },
                                "tax": {
                                    "amount": 316.76,
                                    "currencyCode": "USD",
                                    "convertedAmount": 316.76,
                                    "convertedCurrency": "USD",
                                },
                            },
                        },
                    ]
                }
            },
            "otherServiceInfos": [],
            "disruptedFlightDetails": [
                {
                    "departureDateTime": {"iso8601": "2025-06-12T05:15:00"},
                    "arrivalDateTime": {"iso8601": "2025-06-12T07:20:00"},
                    "cabin": "ECONOMY",
                    "originAirportCode": "SEA",
                    "destinationAirportCode": "OAK",
                    "marketing": {"num": "4069"},
                    "operating": {"num": "4069"},
                },
                {
                    "departureDateTime": {"iso8601": "2025-06-14T17:05:00"},
                    "arrivalDateTime": {"iso8601": "2025-06-14T18:40:00"},
                    "cabin": "ECONOMY",
                    "originAirportCode": "OAK",
                    "destinationAirportCode": "LAS",
                    "marketing": {"num": "4040"},
                    "operating": {"num": "4040"},
                },
                {
                    "departureDateTime": {"iso8601": "2025-06-14T21:55:00"},
                    "arrivalDateTime": {"iso8601": "2025-06-15T00:30:00"},
                    "cabin": "ECONOMY",
                    "originAirportCode": "LAS",
                    "destinationAirportCode": "SEA",
                    "marketing": {"num": "2830"},
                    "operating": {"num": "2830"},
                },
            ],
        },
        "additionalMetadata": {
            "airportInfo": [
                {
                    "airportCode": "OAK",
                    "airportName": "Oakland International Airport",
                    "cityName": "Oakland",
                    "countryName": "United States",
                    "countryCode": "US",
                    "zoneName": "America/Los_Angeles",
                    "stateCode": "CA",
                },
                {
                    "airportCode": "SEA",
                    "airportName": "Seattle–Tacoma International Airport",
                    "cityName": "Seattle",
                    "countryName": "United States",
                    "countryCode": "US",
                    "zoneName": "America/Los_Angeles",
                    "stateCode": "WA",
                },
                {
                    "airportCode": "LAS",
                    "airportName": "Harry Reid International Airport",
                    "cityName": "Las Vegas",
                    "countryName": "United States",
                    "countryCode": "US",
                    "zoneName": "America/Los_Angeles",
                    "stateCode": "NV",
                },
            ],
            "airlineInfo": [{"airlineCode": "WN", "airlineName": "Southwest Airlines"}],
        },
        "preBookAnswers": {"answers": []},
        "customFields": [],
        "customFieldsV3Responses": [],
        "bookingHistory": [
            {
                "bookerInfo": {
                    "name": "OttoTest ApiUser",
                    "email": "<EMAIL>",
                    "role": "AGENT",
                    "tmcName": "",
                },
                "bookingInfo": {
                    "updatedDateTime": {"iso8601": "2025-05-07T17:48:26Z"},
                    "status": "EXCHANGED",
                    "bookingSourceClient": "WEB",
                },
            },
            {
                "bookerInfo": {
                    "name": "OttoTest ApiUser",
                    "email": "<EMAIL>",
                    "role": "AGENT",
                    "tmcName": "",
                },
                "bookingInfo": {
                    "updatedDateTime": {"iso8601": "2025-05-07T16:15:52Z"},
                    "status": "BOOKED",
                    "bookingSourceClient": "WEB",
                },
            },
        ],
        "totalFare": {"amount": 163.3, "currencyCode": "USD", "convertedAmount": 163.3, "convertedCurrency": "USD"},
        "serviceFees": [],
        "paymentInfo": [
            {
                "fop": {
                    "type": "CARD",
                    "card": {
                        "id": "7004652e-291b-4347-83f6-a1d8ec52d34b",
                        "type": "CREDIT",
                        "company": "AMEX",
                        "name": "Chengxuan Wang",
                        "address": {
                            "addressLines": ["3783 Monterey Ct N"],
                            "administrativeArea": "",
                            "administrativeAreaName": "",
                            "description": "",
                            "isDefault": False,
                            "languageCode": "",
                            "locality": "",
                            "locationCode": "",
                            "organization": "",
                            "postalCode": "98057",
                            "continentCode": "",
                            "recipients": [],
                            "regionCode": "US",
                            "regionName": "",
                            "revision": 0,
                            "sortingCode": "",
                            "sublocality": "",
                            "timezone": "",
                        },
                        "number": "3XXXXXXXXXXX0005",
                        "expiryMonth": 1,
                        "expiryYear": 2030,
                        "cvv": "",
                        "label": "OTTO credit card",
                        "currency": "",
                        "externalId": "",
                    },
                    "additionalInfo": "",
                    "accessType": {
                        "accessType": "PERSONAL",
                        "entityIds": ["ff91d58d-60f7-45a0-ad2e-cbf26bb72676"],
                        "entities": [
                            {"entityId": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676", "centralCardAccessLevel": "UNKNOWN"}
                        ],
                    },
                    "paymentMethod": "CREDIT_CARD",
                },
                "totalCharge": {
                    "amount": 163.3,
                    "currencyCode": "USD",
                    "convertedAmount": 163.3,
                    "convertedCurrency": "USD",
                    "otherCoinage": [],
                },
                "totalRefund": {
                    "amount": 0,
                    "currencyCode": "USD",
                    "convertedAmount": 0,
                    "convertedCurrency": "USD",
                    "otherCoinage": [],
                },
                "netCharge": {
                    "amount": 163.3,
                    "currencyCode": "USD",
                    "convertedAmount": 163.3,
                    "convertedCurrency": "USD",
                    "otherCoinage": [],
                },
            }
        ],
        "bookingStatus": "CONFIRMED_STATUS",
        "contactSupport": False,
        "travelerPnrVisibilityStatus": "VISIBLE",
        "pnrCreationDetails": {},
        "tripId": "5148867556",
        "documents": [
            {
                "url": "https://duploservices-sboxmeta-payload-240774922464.s3.us-west-2.amazonaws.com/80453a0e-3c49-452f-89d4-ff824d5161a1/SPOT-US-002094.pdf?X-Amz-Security-Token=IQoJb3JpZ2luX2VjELf%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLXdlc3QtMiJHMEUCIQDbul3Z3a0GFChZegPDUMEqYjRk5WjLfLQe4NSBn717ZgIgLaHOySrMjdnIUlWmDAJ7EicFfVnezX9goAcbrODlGJUq5wMIYBABGgwyNDA3NzQ5MjI0NjQiDOwZlFFTrDc6GOt%2FPCrEA7DYRrJ00qJVMe37JQemcU1ixVFnq5C9p%2F%2BspX3Ue85zQDVHesqg3W1o%2F51uhwCZWnmXvrhc%2BeVvG8aib4lkB%2FRUzSsguVhEZh5UKoEYbUye36aqGvLXzhNblL9%2FNRNerxRsa9dfEudVj6jQN0rUdm%2F3zNVOQ3C57QxJi1PbhRXb0E9vaOcuFTC4Imhkx6rFRL8xiZFwc8mez3yHPmS2a2GXeIgDlxySfM1MEFlaAJWc5p4DL4vSokx8xvYpIX4y9gLxvE2Zqj47HOUwUIBvMmojS%2Fw%2BIl37qDpgyxIx%2BAzzzI6c8eBuhZoIprEF3p1GBLq47wWfvYkT%2FEvCX%2BgEwcahG0wlIlInrtE32D8ZMibnAPuWlTiMODT8MfN%2Bf6W%2Bz2v%2F0FGrcqJQXKQPJ5Z4K6MtbmuXbouGK4H4TEr1FIEXitnyGUJ0RuqPpkhtqq4jYbroQA0RRbuOTm%2Bewpnl%2FPp0hX%2FzKiFP7YhHdyrZW%2BEXHvOzPuxRoQNfSiRE1zSJMzC2LqnjkMzmMv9F8PrTRjlGvXxlM9bIs9oBtyGXKs2ZHoTjJBC5OJgm2kt%2FDp%2BzmDCUOHjI0ey%2F5pJPCIqrtkWORlwOMOPd7cAGOqUB6WlA2FdalvJNNuFrrkNe5yM3sKdbC6TUufDC3yuk%2FcLRiNLoWSteYJb%2FDSiRr33nHplxTEM5Lv7227GoODRRAFDb9UyNRcdIHS1DALy0pLJYBUyk%2F5sav7N8%2F2N2XwOa13SAZddd0Cg7T4b3v5IPu3APd0askJBzPRsEkZ4Wkxrq3eGiDjTq5FJn4osXpEYbQkG0Lqcyimsx9BIQdbRp%2Fz2WnmBq&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250507T174921Z&X-Amz-SignedHeaders=host&X-Amz-Credential=ASIATQD2NYDQIUDAUMHE%2F20250507%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Expires=3600&X-Amz-Signature=679f94550861d8be3f17637ce14ef294bcd1983ecfb3e403c5a9194888113317",
                "documentId": "4a323689-78e3-4589-9ed9-93ce96f51660",
                "documentMetadata": {
                    "documentType": "INVOICE",
                    "entityType": "PNR",
                    "entityId": "6669982242",
                    "entityMetadata": {
                        "pnrMetadata": {},
                        "invoiceMetadata": {"invoiceNumber": "SPOT-US-002094", "invoiceType": "FARE_INVOICE"},
                        "travelType": "AIR",
                    },
                    "name": "SPOT-US-002094.pdf",
                },
            }
        ],
        "pnrId": "6669982242",
        "invoiceInfos": [
            {
                "invoiceNumber": "SPOT-US-002094",
                "productType": "PNR",
                "invoiceId": "73dccdc4-f8ac-4dd6-a5f1-fa536f6452b5",
            },
            {
                "invoiceNumber": "SPOT-US-002097",
                "productType": "PNR",
                "invoiceId": "329f060a-c485-4dde-8d56-104b0c29392a",
            },
        ],
        "totalFareAmount": {
            "base": {"amount": 114.42, "currencyCode": "USD", "convertedAmount": 114.42, "convertedCurrency": "USD"},
            "tax": {"amount": 48.88, "currencyCode": "USD", "convertedAmount": 48.88, "convertedCurrency": "USD"},
        },
        "dkNumber": "0000002558",
        "savingsFare": {
            "fareAmount": {
                "base": {
                    "amount": 114.42,
                    "currencyCode": "USD",
                    "convertedAmount": 114.42,
                    "convertedCurrency": "USD",
                },
                "tax": {"amount": 39.18, "currencyCode": "USD", "convertedAmount": 39.18, "convertedCurrency": "USD"},
            },
            "isTaxIncluded": True,
        },
        "tripUsageMetadata": {"tripUsageType": "STANDARD"},
        "owningPccInfo": {"zoneId": "America/Chicago"},
    },
    "operationSummary": {"pnrUpdateSummary": {"ticketsIssued": ["5267174579427"]}},
}

cancel_event = {
    "timestamp": "2025-05-19T18:22:44.979706622Z",
    "event_type": "PNR_V3",
    "operation": "TICKET_VOIDED",
    "payload": {
        "version": 3,
        "createdVia": "OBT",
        "initialVersionCreatedVia": "OBT",
        "sourceInfo": {
            "sourcePnrId": "NOPHDB",
            "bookingSource": "FARELOGIX_NDC",
            "thirdParty": "FARELOGIX_NDC",
            "bookingDateTime": {"iso8601": "2025-05-15T04:09:08Z"},
            "posDescriptor": "A4L0",
            "iataNumber": "",
        },
        "invoiceDelayedBooking": False,
        "travelers": [
            {
                "user": {
                    "dob": {"iso8601": "1989-12-30"},
                    "email": "<EMAIL>",
                    "gender": "MALE",
                    "name": {"family1": "Wang", "family2": "", "given": "Chengxuan", "middle": "", "preferred": ""},
                    "paymentInfos": [
                        {
                            "applicableTo": [],
                            "card": {
                                "id": "7004652e-291b-4347-83f6-a1d8ec52d34b",
                                "type": "CREDIT",
                                "company": "AMEX",
                                "name": "Chengxuan Wang",
                                "address": {
                                    "addressLines": ["3783 Monterey Ct N"],
                                    "administrativeArea": "WA",
                                    "locality": "Renton",
                                    "postalCode": "98057",
                                    "regionCode": "US",
                                },
                                "number": "3XXXXXXXXXXX0005",
                                "expiryMonth": 1,
                                "expiryYear": 2030,
                                "cvv": "",
                                "label": "OTTO credit card",
                                "expiry": {},
                            },
                            "accessType": "PERSONAL",
                            "access": {
                                "accessType": "PERSONAL",
                                "entityIds": ["ff91d58d-60f7-45a0-ad2e-cbf26bb72676"],
                                "entities": [
                                    {
                                        "entityId": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676",
                                        "centralCardAccessLevel": "UNKNOWN",
                                    }
                                ],
                            },
                        }
                    ],
                    "phoneNumbers": [
                        {
                            "countryCode": 0,
                            "countryCodeSource": "UNSPECIFIED",
                            "extension": "",
                            "isoCountryCode": "",
                            "italianLeadingZero": False,
                            "nationalNumber": 0,
                            "numberOfLeadingZeros": 0,
                            "preferredDomesticCarrierCode": "",
                            "rawInput": "***********",
                            "type": "UNKNOWN_TYPE",
                        }
                    ],
                },
                "userBusinessInfo": {
                    "designation": "",
                    "email": "<EMAIL>",
                    "employeeId": "",
                    "legalEntityId": {"id": "96dd735a-d576-4e68-ad91-5e06da377e2e"},
                    "organizationId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                },
                "userOrgId": {
                    "organizationAgencyId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                    "organizationId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                    "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                    "tmcInfo": {
                        "id": {"id": "ecc5b835-8001-430c-98f8-fedeccebe4cf"},
                        "primaryServiceProviderTmc": {"tmcId": {"id": "ecc5b835-8001-430c-98f8-fedeccebe4cf"}},
                        "secondaryServiceProviderTmcs": [],
                        "partnerTmcId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                    },
                    "tmcBasicInfo": {
                        "contractingTmc": {
                            "id": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                            "name": "Otto Trip",
                            "logo": {"data": "", "dimensions": {"height": 0, "width": 0}, "url": ""},
                        },
                        "bookingTmc": {"id": {"id": "ecc5b835-8001-430c-98f8-fedeccebe4cf"}},
                    },
                },
                "persona": "EMPLOYEE",
                "isActive": True,
                "tier": "BASIC",
            }
        ],
        "pnrTravelers": [
            {
                "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                "travelerInfo": {"userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"}, "adhocTravelerInfo": {}},
                "personalInfo": {
                    "dob": {"iso8601": "1989-12-30"},
                    "email": "<EMAIL>",
                    "gender": "MALE",
                    "name": {"family1": "Wang", "family2": "", "given": "Chengxuan", "middle": "", "preferred": ""},
                    "phoneNumbers": [
                        {
                            "countryCode": 0,
                            "countryCodeSource": "UNSPECIFIED",
                            "extension": "",
                            "isoCountryCode": "",
                            "italianLeadingZero": False,
                            "nationalNumber": 0,
                            "numberOfLeadingZeros": 0,
                            "preferredDomesticCarrierCode": "",
                            "rawInput": "***********",
                            "type": "UNKNOWN_TYPE",
                        }
                    ],
                },
                "loyalties": [],
                "persona": "EMPLOYEE",
                "businessInfo": {
                    "legalEntity": {
                        "id": "96dd735a-d576-4e68-ad91-5e06da377e2e",
                        "name": "Otto Trip, Inc.",
                        "ein": "",
                        "externalId": "",
                        "companySpecifiedAttributes": [],
                    },
                    "companyId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                    "companyInfo": {
                        "id": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                        "name": "Otto Trip",
                        "externalId": "",
                    },
                },
                "tier": "BASIC",
            }
        ],
        "costOfGoodsSold": {
            "payments": [
                {
                    "travelerIndices": [0],
                    "userIds": [{"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"}],
                    "fop": {
                        "type": "CARD",
                        "card": {
                            "id": "7004652e-291b-4347-83f6-a1d8ec52d34b",
                            "type": "CREDIT",
                            "company": "AMEX",
                            "name": "Chengxuan Wang",
                            "address": {
                                "addressLines": ["3783 Monterey Ct N"],
                                "administrativeArea": "WA",
                                "administrativeAreaName": "",
                                "description": "",
                                "isDefault": False,
                                "languageCode": "",
                                "locality": "Renton",
                                "locationCode": "",
                                "organization": "",
                                "postalCode": "98057",
                                "continentCode": "",
                                "recipients": [],
                                "regionCode": "US",
                                "regionName": "",
                                "revision": 0,
                                "sortingCode": "",
                                "sublocality": "",
                                "timezone": "",
                            },
                            "number": "3XXXXXXXXXXX0005",
                            "expiryMonth": 1,
                            "expiryYear": 2030,
                            "cvv": "",
                            "label": "OTTO credit card",
                            "currency": "",
                            "externalId": "",
                            "vaultId": "00000000-0000-0000-0000-000000000000",
                            "expiry": {},
                        },
                        "additionalInfo": "",
                        "accessType": {
                            "accessType": "PERSONAL",
                            "entityIds": ["ff91d58d-60f7-45a0-ad2e-cbf26bb72676"],
                            "entities": [
                                {
                                    "entityId": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676",
                                    "centralCardAccessLevel": "UNKNOWN",
                                }
                            ],
                        },
                        "paymentMethod": "CREDIT_CARD",
                        "paymentMetadata": {
                            "cardMetadata": {
                                "card": {
                                    "id": "7004652e-291b-4347-83f6-a1d8ec52d34b",
                                    "type": "CREDIT",
                                    "company": "AMEX",
                                    "name": "Chengxuan Wang",
                                    "address": {
                                        "addressLines": ["3783 Monterey Ct N"],
                                        "administrativeArea": "WA",
                                        "administrativeAreaName": "",
                                        "description": "",
                                        "isDefault": False,
                                        "languageCode": "",
                                        "locality": "Renton",
                                        "locationCode": "",
                                        "organization": "",
                                        "postalCode": "98057",
                                        "continentCode": "",
                                        "recipients": [],
                                        "regionCode": "US",
                                        "regionName": "",
                                        "revision": 0,
                                        "sortingCode": "",
                                        "sublocality": "",
                                        "timezone": "",
                                    },
                                    "number": "3XXXXXXXXXXX0005",
                                    "expiryMonth": 1,
                                    "expiryYear": 2030,
                                    "cvv": "",
                                    "label": "OTTO credit card",
                                    "currency": "",
                                    "externalId": "",
                                    "vaultId": "00000000-0000-0000-0000-000000000000",
                                    "expiry": {},
                                },
                                "accessType": {
                                    "accessType": "PERSONAL",
                                    "entityIds": ["ff91d58d-60f7-45a0-ad2e-cbf26bb72676"],
                                    "entities": [
                                        {
                                            "entityId": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676",
                                            "centralCardAccessLevel": "UNKNOWN",
                                        }
                                    ],
                                },
                                "isLodgeCard": False,
                            }
                        },
                        "paymentSourceType": "CARD",
                    },
                    "paymentReference": "",
                    "paymentType": "FLIGHTS",
                    "paymentThirdParty": "UNKNOWN_PARTY",
                    "paymentId": "",
                    "paymentGateway": "PAYMENT_GATEWAY_UNKNOWN",
                    "isRefunded": False,
                    "networkTransactionId": "",
                }
            ]
        },
        "isFinalized": True,
        "policyInfo": {
            "outOfPolicy": False,
            "reasonCode": "UNKNOWN_CHECKOUT_ANSWER_TYPE",
            "reason": "",
            "appliedPolicyInfo": {
                "policies": [{"id": "366011aa-9ad6-4cab-bb6b-add9ef060c33", "version": "0"}],
                "ruleResultInfos": [],
            },
        },
        "airPnr": {
            "legs": [
                {
                    "flights": [
                        {
                            "departureDateTime": {"iso8601": "2025-09-08T15:19:00"},
                            "arrivalDateTime": {"iso8601": "2025-09-08T17:35:00"},
                            "duration": {"iso8601": "PT2H16M"},
                            "flightId": "CgNTRUESA1NGTxoVChMyMDI1LTA5LTA4VDE1OjE5OjAwIhUKEzIwMjUtMDktMDhUMTc6MzU6MDA=",
                            "origin": "SEA",
                            "destination": "SFO",
                            "departureGate": {"gate": "", "terminal": ""},
                            "arrivalGate": {"gate": "", "terminal": "3"},
                            "marketing": {"num": "2355", "airlineCode": "UA"},
                            "operating": {"num": "2355", "airlineCode": "UA"},
                            "operatingAirlineName": "",
                            "hiddenStops": [],
                            "vendorConfirmationNumber": "N8HRZ1",
                            "cabin": "ECONOMY",
                            "bookingCode": "K",
                            "flightStatus": "CANCELLED",
                            "otherStatuses": [],
                            "co2EmissionDetail": {
                                "emissionValue": 0.174944,
                                "averageEmissionValue": 0.1988,
                                "flightDistanceKm": 1190,
                                "isApproximate": False,
                            },
                            "restrictions": [],
                            "sourceStatus": "None",
                            "equipment": {"code": "738", "type": "", "name": "Boeing 737-800"},
                            "distance": {"length": 678, "unit": "MILE"},
                            "flightWaiverCodes": [],
                            "amenities": [{}, {}, {}, {}, {}, {}, {}, {}],
                            "flightIndex": 0,
                        }
                    ],
                    "brandName": "Economy",
                    "validatingAirlineCode": "UA",
                    "legStatus": "CANCELLED_STATUS",
                    "sortingPriority": 0,
                    "travelerRestrictions": [
                        {
                            "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                            "restrictions": [
                                "SEAT_EDIT_NOT_ALLOWED",
                                "LOYALTY_EDIT_NOT_ALLOWED",
                                "KTN_EDIT_NOT_ALLOWED",
                                "REDRESS_EDIT_NOT_ALLOWED",
                                "SSR_EDIT_NOT_ALLOWED",
                                "OSI_EDIT_NOT_ALLOWED",
                            ],
                        }
                    ],
                    "fareOffers": [
                        {
                            "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                            "baggagePolicy": {
                                "checkedIn": [
                                    {"description": "1 checked bag, 50 lbs (40 USD)"},
                                    {"description": "+1 checked bag, 50 lbs (50 USD)"},
                                ],
                                "carryOn": [{"description": "1 carry-on bag"}],
                            },
                        }
                    ],
                    "legId": "CgNTRUESA1NGTxoKNjk1MjU5MTQ4NQ==",
                    "rateType": "PUBLISHED",
                    "preferredTypes": [],
                    "preferences": [],
                    "legIndex": 0,
                }
            ],
            "airPnrRemarks": [],
            "travelerInfos": [
                {
                    "airVendorCancellationInfo": {"airVendorCancellationObjects": []},
                    "createdMcos": [],
                    "travelerIdx": 0,
                    "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                    "paxType": "ADULT",
                    "tickets": [
                        {
                            "ticketNumber": "0164300139310",
                            "ticketType": "ANCILLARY",
                            "issuedDateTime": {"iso8601": "2025-05-14T23:09:00"},
                            "status": "VOIDED",
                            "amount": {
                                "base": {
                                    "amount": 86.99,
                                    "currencyCode": "USD",
                                    "convertedAmount": 86.99,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "tax": {
                                    "amount": 6.52,
                                    "currencyCode": "USD",
                                    "convertedAmount": 6.52,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                            },
                            "flightCoupons": [],
                            "ancillaries": [],
                            "validatingAirlineCode": "UA",
                            "taxBreakdown": {
                                "tax": [
                                    {
                                        "amount": {
                                            "amount": 6.52,
                                            "currencyCode": "USD",
                                            "convertedAmount": 6.52,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "taxCode": "US",
                                    }
                                ]
                            },
                            "iataNumber": "45526666",
                            "fareCalculation": "",
                            "updateDateTime": {"iso8601": "2025-05-19T00:00:00"},
                            "paymentDetails": [],
                            "ticketSettlement": "NON_ARC_BSP_TICKET",
                            "vendorCancellationId": "",
                            "conjunctionTicketSuffix": [],
                            "pcc": "A4L0",
                            "ticketIncompleteReasons": ["SYS_TICKET"],
                        }
                    ],
                    "boardingPass": [],
                    "booking": {
                        "seats": [],
                        "luggageDetails": [],
                        "otherAncillaries": [],
                        "itinerary": {
                            "totalFare": {
                                "base": {
                                    "amount": 91.33,
                                    "currencyCode": "USD",
                                    "convertedAmount": 91.33,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "tax": {
                                    "amount": 22.15,
                                    "currencyCode": "USD",
                                    "convertedAmount": 22.15,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                            },
                            "totalFlightsFare": {
                                "base": {
                                    "amount": 91.33,
                                    "currencyCode": "USD",
                                    "convertedAmount": 91.33,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "tax": {
                                    "amount": 22.15,
                                    "currencyCode": "USD",
                                    "convertedAmount": 22.15,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                            },
                            "flightFareBreakup": [
                                {
                                    "legIndices": [0],
                                    "flightsFare": {
                                        "base": {
                                            "amount": 91.33,
                                            "currencyCode": "USD",
                                            "convertedAmount": 91.33,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "tax": {
                                            "amount": 22.15,
                                            "currencyCode": "USD",
                                            "convertedAmount": 22.15,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                    },
                                }
                            ],
                            "fareComponents": [
                                {
                                    "fareBasisCode": "KAA2AQDS",
                                    "tourCode": "",
                                    "ticketDesignator": "",
                                    "baseFare": {
                                        "amount": 91.33,
                                        "currencyCode": "USD",
                                        "convertedAmount": 91.33,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "flightIds": [{"legIdx": 0, "flightIdx": 0}],
                                }
                            ],
                            "otherAncillaryFares": [],
                        },
                        "otherCharges": [],
                    },
                    "appliedCredits": [],
                    "specialServiceRequestInfos": [],
                }
            ],
            "bookingMetadata": {
                "fareStatistics": {
                    "statisticsItems": [
                        {
                            "statisticType": "MINIMUM",
                            "totalFare": {
                                "base": {
                                    "amount": 46.68,
                                    "currencyCode": "USD",
                                    "convertedAmount": 46.68,
                                    "convertedCurrency": "USD",
                                },
                                "tax": {
                                    "amount": 28.5,
                                    "currencyCode": "USD",
                                    "convertedAmount": 28.5,
                                    "convertedCurrency": "USD",
                                },
                            },
                        },
                        {
                            "statisticType": "MEDIAN",
                            "totalFare": {
                                "base": {
                                    "amount": 221.56,
                                    "currencyCode": "USD",
                                    "convertedAmount": 221.56,
                                    "convertedCurrency": "USD",
                                },
                                "tax": {
                                    "amount": 52.42,
                                    "currencyCode": "USD",
                                    "convertedAmount": 52.42,
                                    "convertedCurrency": "USD",
                                },
                            },
                        },
                        {
                            "statisticType": "MAXIMUM",
                            "totalFare": {
                                "base": {
                                    "amount": 2998.31,
                                    "currencyCode": "USD",
                                    "convertedAmount": 2998.31,
                                    "convertedCurrency": "USD",
                                },
                                "tax": {
                                    "amount": 260.67,
                                    "currencyCode": "USD",
                                    "convertedAmount": 260.67,
                                    "convertedCurrency": "USD",
                                },
                            },
                        },
                    ]
                }
            },
            "otherServiceInfos": [],
        },
        "additionalMetadata": {
            "airportInfo": [
                {
                    "airportCode": "SFO",
                    "airportName": "San Francisco International Airport",
                    "cityName": "San Francisco",
                    "countryName": "United States",
                    "countryCode": "US",
                    "zoneName": "America/Los_Angeles",
                    "stateCode": "CA",
                },
                {
                    "airportCode": "SEA",
                    "airportName": "Seattle–Tacoma International Airport",
                    "cityName": "Seattle",
                    "countryName": "United States",
                    "countryCode": "US",
                    "zoneName": "America/Los_Angeles",
                    "stateCode": "WA",
                },
            ],
            "airlineInfo": [{"airlineCode": "UA", "airlineName": "United Airlines"}],
        },
        "preBookAnswers": {"answers": []},
        "customFields": [],
        "customFieldsV3Responses": [],
        "bookingHistory": [
            {
                "bookerInfo": {
                    "name": "OttoTest ApiUser",
                    "email": "<EMAIL>",
                    "role": "AGENT",
                    "tmcName": "",
                },
                "bookingInfo": {
                    "updatedDateTime": {"iso8601": "2025-05-15T04:09:08Z"},
                    "status": "BOOKED",
                    "bookingSourceClient": "WEB",
                },
            }
        ],
        "serviceFees": [],
        "paymentInfo": [
            {
                "fop": {
                    "type": "CARD",
                    "card": {
                        "id": "7004652e-291b-4347-83f6-a1d8ec52d34b",
                        "type": "CREDIT",
                        "company": "AMEX",
                        "name": "Chengxuan Wang",
                        "address": {
                            "addressLines": ["3783 Monterey Ct N"],
                            "administrativeArea": "WA",
                            "administrativeAreaName": "",
                            "description": "",
                            "isDefault": False,
                            "languageCode": "",
                            "locality": "Renton",
                            "locationCode": "",
                            "organization": "",
                            "postalCode": "98057",
                            "continentCode": "",
                            "recipients": [],
                            "regionCode": "US",
                            "regionName": "",
                            "revision": 0,
                            "sortingCode": "",
                            "sublocality": "",
                            "timezone": "",
                        },
                        "number": "3XXXXXXXXXXX0005",
                        "expiryMonth": 1,
                        "expiryYear": 2030,
                        "cvv": "",
                        "label": "OTTO credit card",
                        "currency": "",
                        "externalId": "",
                        "vaultId": "00000000-0000-0000-0000-000000000000",
                        "expiry": {},
                    },
                    "additionalInfo": "",
                    "accessType": {"accessType": "PERSONAL", "entityIds": [], "entities": []},
                    "paymentMethod": "CREDIT_CARD",
                },
                "totalCharge": {
                    "amount": 206.99,
                    "currencyCode": "USD",
                    "convertedAmount": 206.99,
                    "convertedCurrency": "USD",
                    "otherCoinage": [],
                },
                "totalRefund": {
                    "amount": 206.99,
                    "currencyCode": "USD",
                    "convertedAmount": 206.99,
                    "convertedCurrency": "USD",
                    "otherCoinage": [],
                },
                "netCharge": {
                    "amount": 0,
                    "currencyCode": "USD",
                    "convertedAmount": 0,
                    "convertedCurrency": "USD",
                    "otherCoinage": [],
                },
            }
        ],
        "bookingStatus": "CANCELLED_STATUS",
        "contactSupport": False,
        "travelerPnrVisibilityStatus": "VISIBLE",
        "pnrCreationDetails": {},
        "tripId": "8336175163",
        "documents": [],
        "pnrId": "6952591485",
        "totalFareAmount": {
            "base": {"amount": 178.32, "currencyCode": "USD", "convertedAmount": 178.32, "convertedCurrency": "USD"},
            "tax": {"amount": 28.67, "currencyCode": "USD", "convertedAmount": 28.67, "convertedCurrency": "USD"},
        },
        "savingsFare": {
            "fareAmount": {
                "base": {"amount": 91.33, "currencyCode": "USD", "convertedAmount": 91.33, "convertedCurrency": "USD"},
                "tax": {"amount": 22.15, "currencyCode": "USD", "convertedAmount": 22.15, "convertedCurrency": "USD"},
            },
            "isTaxIncluded": True,
        },
        "tripUsageMetadata": {"tripUsageType": "STANDARD"},
        "owningPccInfo": {"zoneId": "America/Chicago"},
    },
    "operationSummary": {"pnrUpdateSummary": {"ticketsVoided": ["0164300139310"]}},
}

booked_event = {
    "timestamp": "2025-03-11T21:20:29.185998532Z",
    "event_type": "PNR_V3",
    "operation": "BOOKING_TICKETED",
    "payload": {
        "version": 5,
        "createdVia": "OBT",
        "initialVersionCreatedVia": "OBT",
        "sourceInfo": {
            "sourcePnrId": "TVFGKK",
            "bookingSource": "SABRE",
            "thirdParty": "SABRE",
            "bookingDateTime": {"iso8601": "2025-03-11T21:19:39Z"},
            "posDescriptor": "1ZSK",
            "iataNumber": "",
        },
        "invoiceDelayedBooking": False,
        "travelers": [
            {
                "travelerPersonalInfo": {
                    "loyaltyInfos": [{"appliedTo": ["DL"], "id": "9976197831", "issuedBy": "DL", "type": "AIR"}],
                    "travelPref": {},
                },
                "user": {
                    "dob": {"iso8601": "1989-12-30"},
                    "email": "<EMAIL>",
                    "gender": "MALE",
                    "name": {"family1": "Wang", "family2": "", "given": "Chengxuan", "middle": "", "preferred": ""},
                    "paymentInfos": [
                        {
                            "applicableTo": [],
                            "card": {
                                "id": "9cd9b509-717a-45bd-a321-5ed089436590",
                                "type": "CREDIT",
                                "company": "AMEX",
                                "name": "Chengxuan Wang",
                                "address": {
                                    "addressLines": ["3783 Monterey Ct N"],
                                    "postalCode": "98057",
                                    "regionCode": "US",
                                },
                                "number": "3XXXXXXXXXXX0005",
                                "expiryMonth": 1,
                                "expiryYear": 2030,
                                "cvv": "",
                                "label": "OTTO credit card",
                            },
                            "accessType": "PERSONAL",
                            "access": {
                                "accessType": "PERSONAL",
                                "entityIds": ["ff91d58d-60f7-45a0-ad2e-cbf26bb72676"],
                                "entities": [
                                    {
                                        "entityId": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676",
                                        "centralCardAccessLevel": "UNKNOWN",
                                    }
                                ],
                            },
                        }
                    ],
                    "phoneNumbers": [
                        {
                            "countryCode": 1,
                            "countryCodeSource": "UNSPECIFIED",
                            "extension": "",
                            "isoCountryCode": "US",
                            "italianLeadingZero": False,
                            "nationalNumber": 0,
                            "numberOfLeadingZeros": 0,
                            "preferredDomesticCarrierCode": "",
                            "rawInput": "2672419322",
                            "type": "MOBILE",
                        }
                    ],
                },
                "userBusinessInfo": {
                    "designation": "",
                    "email": "<EMAIL>",
                    "employeeId": "",
                    "legalEntityId": {"id": "96dd735a-d576-4e68-ad91-5e06da377e2e"},
                    "organizationId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                },
                "userOrgId": {
                    "organizationAgencyId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                    "organizationId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                    "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                    "tmcInfo": {
                        "id": {"id": "ecc5b835-8001-430c-98f8-fedeccebe4cf"},
                        "primaryServiceProviderTmc": {"tmcId": {"id": "ecc5b835-8001-430c-98f8-fedeccebe4cf"}},
                        "secondaryServiceProviderTmcs": [],
                        "partnerTmcId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                    },
                    "tmcBasicInfo": {
                        "contractingTmc": {"id": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}},
                        "bookingTmc": {"id": {"id": "ecc5b835-8001-430c-98f8-fedeccebe4cf"}},
                    },
                },
                "persona": "EMPLOYEE",
                "isActive": True,
                "tier": "BASIC",
            }
        ],
        "pnrTravelers": [
            {
                "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                "travelerInfo": {"userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"}, "adhocTravelerInfo": {}},
                "personalInfo": {
                    "dob": {"iso8601": "1989-12-30"},
                    "email": "<EMAIL>",
                    "gender": "MALE",
                    "name": {"family1": "Wang", "family2": "", "given": "Chengxuan", "middle": "", "preferred": ""},
                    "phoneNumbers": [
                        {
                            "countryCode": 1,
                            "countryCodeSource": "UNSPECIFIED",
                            "extension": "",
                            "isoCountryCode": "US",
                            "italianLeadingZero": False,
                            "nationalNumber": 0,
                            "numberOfLeadingZeros": 0,
                            "preferredDomesticCarrierCode": "",
                            "rawInput": "2672419322",
                            "type": "MOBILE",
                        }
                    ],
                },
                "loyalties": [{"appliedTo": ["DL"], "id": "9976197831", "issuedBy": "DL", "type": "AIR"}],
                "persona": "EMPLOYEE",
                "businessInfo": {
                    "legalEntity": {
                        "id": "96dd735a-d576-4e68-ad91-5e06da377e2e",
                        "name": "Otto Trip, Inc.",
                        "ein": "",
                        "externalId": "",
                        "companySpecifiedAttributes": [],
                    },
                    "companyId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                    "companyInfo": {
                        "id": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"},
                        "name": "Otto Trip",
                        "externalId": "",
                    },
                },
                "tier": "BASIC",
            }
        ],
        "costOfGoodsSold": {
            "payments": [
                {
                    "travelerIndices": [0],
                    "userIds": [{"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"}],
                    "fop": {
                        "type": "CARD",
                        "card": {
                            "id": "9cd9b509-717a-45bd-a321-5ed089436590",
                            "type": "CREDIT",
                            "company": "AMEX",
                            "name": "Chengxuan Wang",
                            "address": {
                                "addressLines": ["3783 Monterey Ct N"],
                                "administrativeArea": "",
                                "administrativeAreaName": "",
                                "description": "",
                                "isDefault": False,
                                "languageCode": "",
                                "locality": "Renton",
                                "locationCode": "",
                                "organization": "",
                                "postalCode": "98057",
                                "continentCode": "",
                                "recipients": [],
                                "regionCode": "US",
                                "regionName": "",
                                "revision": 0,
                                "sortingCode": "",
                                "sublocality": "",
                                "timezone": "",
                            },
                            "number": "3XXXXXXXXXXX0005",
                            "expiryMonth": 1,
                            "expiryYear": 2030,
                            "cvv": "",
                            "label": "OTTO credit card",
                            "currency": "",
                            "externalId": "",
                            "vaultId": "00000000-0000-0000-0000-000000000000",
                            "expiry": {},
                        },
                        "additionalInfo": "",
                        "accessType": {
                            "accessType": "PERSONAL",
                            "entityIds": ["ff91d58d-60f7-45a0-ad2e-cbf26bb72676"],
                            "entities": [
                                {
                                    "entityId": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676",
                                    "centralCardAccessLevel": "UNKNOWN",
                                }
                            ],
                        },
                        "paymentMethod": "CREDIT_CARD",
                        "paymentMetadata": {
                            "cardMetadata": {
                                "card": {
                                    "id": "9cd9b509-717a-45bd-a321-5ed089436590",
                                    "type": "CREDIT",
                                    "company": "AMEX",
                                    "name": "Chengxuan Wang",
                                    "address": {
                                        "addressLines": ["3783 Monterey Ct N"],
                                        "administrativeArea": "",
                                        "administrativeAreaName": "",
                                        "description": "",
                                        "isDefault": False,
                                        "languageCode": "",
                                        "locality": "Renton",
                                        "locationCode": "",
                                        "organization": "",
                                        "postalCode": "98057",
                                        "continentCode": "",
                                        "recipients": [],
                                        "regionCode": "US",
                                        "regionName": "",
                                        "revision": 0,
                                        "sortingCode": "",
                                        "sublocality": "",
                                        "timezone": "",
                                    },
                                    "number": "3XXXXXXXXXXX0005",
                                    "expiryMonth": 1,
                                    "expiryYear": 2030,
                                    "cvv": "",
                                    "label": "OTTO credit card",
                                    "currency": "",
                                    "externalId": "",
                                    "vaultId": "00000000-0000-0000-0000-000000000000",
                                    "expiry": {},
                                },
                                "accessType": {
                                    "accessType": "PERSONAL",
                                    "entityIds": ["ff91d58d-60f7-45a0-ad2e-cbf26bb72676"],
                                    "entities": [
                                        {
                                            "entityId": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676",
                                            "centralCardAccessLevel": "UNKNOWN",
                                        }
                                    ],
                                },
                                "isLodgeCard": False,
                            }
                        },
                        "paymentSourceType": "CARD",
                    },
                    "paymentReference": "",
                    "paymentType": "FLIGHTS",
                    "paymentThirdParty": "UNKNOWN_PARTY",
                    "paymentId": "",
                    "paymentGateway": "PAYMENT_GATEWAY_UNKNOWN",
                    "isRefunded": False,
                    "networkTransactionId": "",
                }
            ]
        },
        "transactions": [
            {
                "ctc": [
                    {
                        "isRefunded": False,
                        "amount": {
                            "amount": 118.48,
                            "currencyCode": "USD",
                            "convertedAmount": 118.48,
                            "convertedCurrency": "USD",
                        },
                        "fop": {
                            "type": "CARD",
                            "card": {
                                "id": "9cd9b509-717a-45bd-a321-5ed089436590",
                                "type": "CREDIT",
                                "company": "AMEX",
                                "name": "Chengxuan Wang",
                                "address": {
                                    "addressLines": ["3783 Monterey Ct N"],
                                    "postalCode": "98057",
                                    "regionCode": "US",
                                },
                                "number": "3XXXXXXXXXXX0005",
                                "expiryMonth": 1,
                                "expiryYear": 2030,
                                "cvv": "",
                                "label": "OTTO credit card",
                            },
                            "additionalInfo": "",
                            "paymentMethod": "CREDIT_CARD",
                        },
                        "isGuarantee": False,
                    }
                ],
                "itemGroups": [
                    {
                        "transactionType": "AIR_TICKET_ISSUED",
                        "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                        "confirmationNumber": "0067173776555",
                        "transactionDateTime": {"iso8601": "2025-03-11T21:20:26"},
                        "transactionAmountDiff": {
                            "base": {"amount": 0, "currencyCode": "", "convertedAmount": 0, "convertedCurrency": ""},
                            "tax": {"amount": 0, "currencyCode": "", "convertedAmount": 0, "convertedCurrency": ""},
                        },
                        "totalAmountDiff": {
                            "base": {"amount": 0, "currencyCode": "", "convertedAmount": 0, "convertedCurrency": ""},
                            "tax": {"amount": 0, "currencyCode": "", "convertedAmount": 0, "convertedCurrency": ""},
                        },
                        "transactionAmount": {
                            "base": {
                                "amount": 95.98,
                                "currencyCode": "USD",
                                "convertedAmount": 95.98,
                                "convertedCurrency": "USD",
                            },
                            "tax": {
                                "amount": 22.5,
                                "currencyCode": "USD",
                                "convertedAmount": 22.5,
                                "convertedCurrency": "USD",
                            },
                        },
                        "totalAmount": {
                            "base": {
                                "amount": 95.98,
                                "currencyCode": "USD",
                                "convertedAmount": 95.98,
                                "convertedCurrency": "USD",
                            },
                            "tax": {
                                "amount": 22.5,
                                "currencyCode": "USD",
                                "convertedAmount": 22.5,
                                "convertedCurrency": "USD",
                            },
                        },
                        "invoiceData": {
                            "invoiceNumber": "SPOT-US-001847",
                            "buyer": {
                                "name": "Chengxuan Wang",
                                "address": "Otto Trip, Inc.,\n11 Brooke Dr,\nNovato,\nCA US 94947",
                                "idInfo": [],
                            },
                            "seller": {
                                "name": "test",
                                "address": "test,\ndfgasgsaasf,\ndfsadadf US asdfdsa",
                                "idInfo": [{"idType": "EIN", "value": "3454"}],
                            },
                        },
                        "items": [
                            {
                                "itemType": "AIR_ITEM",
                                "airItemType": "FLIGHT",
                                "flights": [
                                    {
                                        "arrivalDateTime": {"iso8601": "2025-05-31T00:09:00"},
                                        "departureDateTime": {"iso8601": "2025-05-30T21:55:00"},
                                        "marketing": {"num": "2635", "airlineCode": "DL"},
                                        "operating": {"num": "2635", "airlineCode": "DL"},
                                        "origin": {
                                            "airportCode": "SEA",
                                            "airportName": "Seattle–Tacoma International Airport",
                                            "cityCode": "",
                                        },
                                        "destination": {
                                            "airportCode": "SFO",
                                            "airportName": "San Francisco International Airport",
                                            "cityCode": "",
                                        },
                                    }
                                ],
                                "ancillaryTypes": [],
                                "oldFlights": [],
                            }
                        ],
                    }
                ],
                "pnrVersion": 5,
            }
        ],
        "isFinalized": True,
        "policyInfo": {
            "outOfPolicy": False,
            "reasonCode": "UNKNOWN_CHECKOUT_ANSWER_TYPE",
            "reason": "",
            "appliedPolicyInfo": {
                "policies": [{"id": "366011aa-9ad6-4cab-bb6b-add9ef060c33", "version": "0"}],
                "ruleResultInfos": [],
            },
        },
        "airPnr": {
            "legs": [
                {
                    "flights": [
                        {
                            "departureDateTime": {"iso8601": "2025-05-30T21:55:00"},
                            "arrivalDateTime": {"iso8601": "2025-05-31T00:09:00"},
                            "duration": {"iso8601": "PT2H19M"},
                            "flightId": "CgNTRUESA1NGTxoVChMyMDI1LTA1LTMwVDIxOjU1OjAwIhUKEzIwMjUtMDUtMzFUMDA6MDk6MDA=",
                            "origin": "SEA",
                            "destination": "SFO",
                            "departureGate": {"gate": "", "terminal": ""},
                            "arrivalGate": {"gate": "1", "terminal": "TERMINAL 1"},
                            "marketing": {"num": "2635", "airlineCode": "DL"},
                            "operating": {"num": "2635", "airlineCode": "DL"},
                            "operatingAirlineName": "",
                            "hiddenStops": [],
                            "vendorConfirmationNumber": "G57T4F",
                            "cabin": "ECONOMY",
                            "bookingCode": "V",
                            "flightStatus": "CONFIRMED",
                            "otherStatuses": [],
                            "co2EmissionDetail": {
                                "emissionValue": 0.157608,
                                "averageEmissionValue": 0.1791,
                                "flightDistanceKm": 1190,
                                "isApproximate": False,
                            },
                            "restrictions": [],
                            "sourceStatus": "HK",
                            "equipment": {"code": "223", "type": "", "name": "Airbus A220-300"},
                            "distance": {"length": 678, "unit": "MILE"},
                            "flightWaiverCodes": [],
                            "amenities": [{}, {}, {}, {}, {}, {}, {}, {}],
                            "flightIndex": 0,
                        }
                    ],
                    "brandName": "Main Cabin",
                    "validatingAirlineCode": "DL",
                    "legStatus": "CONFIRMED_STATUS",
                    "sortingPriority": 0,
                    "travelerRestrictions": [],
                    "fareOffers": [
                        {
                            "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                            "baggagePolicy": {
                                "checkedIn": [{"description": "1 checked bag, 50 lbs (35 USD)"}],
                                "carryOn": [{"description": "1 carry-on bag"}],
                            },
                        },
                        {
                            "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                            "cancellationPolicy": {"description": "Non-refundable"},
                        },
                        {
                            "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                            "exchangePolicy": {"description": "Change allowed for free"},
                        },
                    ],
                    "legId": "CgNTRUESA1NGTxoKNDM5NDc1MzQyMw==",
                    "rateType": "PUBLISHED",
                    "preferredTypes": [],
                    "preferences": [],
                    "legIndex": 0,
                }
            ],
            "airPnrRemarks": [
                {"remarkString": "OTTO TRIP, INC."},
                {"remarkString": "HTL-RATECODE-THR"},
                {"remarkString": "TRIPFEE-BC0.00/EXCX0.00"},
                {"remarkString": "ISPASSIVEPNR FALSE"},
                {"remarkString": "  AUTH-AVS BILLING ADDRESS AND ZIP POSTAL ARE MATC"},
                {"remarkString": "AUTH-AMEX/AX0005/11MAR/01731741728014548175       "},
                {"remarkString": "S*UD78 IN POLICY"},
                {"remarkString": "*AX3XXXXXXXXXX0005¥01/30-XN"},
                {"remarkString": "TRACEID 2449BB1029BE90D5"},
                {"remarkString": "BOOKEDBYORGID 4ECABB34-5EB3-4192-A1C8-C634A151DC41"},
                {"remarkString": "XXAUTH/Y12688/AX3----------0005/DL/USD118.48/11MAR/S"},
                {"remarkString": "2-SABREPROFILES¥OTTO TRIP INC."},
                {"remarkString": "S*HU"},
                {"remarkString": "PHONESPOTNANA"},
                {"remarkString": "S*UD3 7695338655"},
                {"remarkString": "MAILBOXSPOTNANA"},
                {"remarkString": "CURR-USD"},
                {"remarkString": "S*UD166 SPOTNANA"},
                {"remarkString": "11 BROOKE DR"},
                {"remarkString": "NO-COMMISSION-APPLIES-1741728002"},
                {"remarkString": "HTL-RATECODE-WTH"},
                {"remarkString": "TRAVELERPID FF91D58D-60F7-45A0-AD2E-CBF26BB72676"},
                {"remarkString": "BOOKEDBYUSERID 2A8658AD-0572-480C-BBC0-B8BD3B3FB78C"},
                {"remarkString": "TRAVELERORGID 4ECABB34-5EB3-4192-A1C8-C634A151DC41"},
                {"remarkString": "  AUTH-APV/Y12688/000/USD118.48                   "},
                {"remarkString": "S*UD212 OTTO TRIP INC."},
                {"remarkString": "NOVATO CA US 94947"},
                {"remarkString": "PNRID 4394753423"},
                {"remarkString": "S*ICSPOTNANA"},
                {"remarkString": "PNRTYPE AIR"},
                {"remarkString": "XXTAW/"},
                {"remarkString": "ENVIRONMENT SBOXMETA"},
                {"remarkString": "AA-TCC859996"},
                {"remarkString": "HTL-RATECODE-SIG"},
                {"remarkString": "WORKFLOWID 2558EA1F702421B4"},
                {"remarkString": "S*SA804"},
                {"remarkString": "BA-TCC859996"},
                {"remarkString": "HTL-RATECODE-ABC"},
                {"remarkString": "HTL-RATECODE-FHD"},
                {"remarkString": "OBT-COMMISSION-SUCCESS-1741728002"},
                {"remarkString": "OBT-COMMISSION-PROCESSED-1741728002"},
                {"remarkString": "HTL-RATECODE-PP6"},
                {"remarkString": "PPT DOB-12/30/1989 WANG/CHENGXUAN -M"},
                {"remarkString": "NO EMAIL"},
                {"remarkString": "HTL-RATECODE-FHP"},
                {"remarkString": "TRIPID 7695338655"},
                {"remarkString": "AIR-SEQ1"},
                {"remarkString": "  AUTH-CSC MATCHED/Y                              "},
                {"remarkString": "IB-TCC859996"},
            ],
            "travelerInfos": [
                {
                    "airVendorCancellationInfo": {"airVendorCancellationObjects": []},
                    "createdMcos": [],
                    "travelerIdx": 0,
                    "userId": {"id": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676"},
                    "paxType": "ADULT",
                    "tickets": [
                        {
                            "ticketNumber": "0067173776555",
                            "ticketType": "FLIGHT",
                            "issuedDateTime": {"iso8601": "2025-03-11T00:00:00"},
                            "issuedDateTimeWithZone": {"iso8601": "2025-03-11T00:00:00Z"},
                            "status": "ISSUED",
                            "amount": {
                                "base": {
                                    "amount": 95.98,
                                    "currencyCode": "USD",
                                    "convertedAmount": 95.98,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "tax": {
                                    "amount": 22.5,
                                    "currencyCode": "USD",
                                    "convertedAmount": 22.5,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                            },
                            "flightCoupons": [{"legIdx": 0, "flightIdx": 0, "status": "NOT_FLOWN"}],
                            "ancillaries": [],
                            "validatingAirlineCode": "DL",
                            "exchangePolicy": {
                                "exchangePenalty": {
                                    "amount": 0,
                                    "currencyCode": "USD",
                                    "convertedAmount": 0,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "isExchangeable": True,
                                "isCat16": False,
                                "isConditional": True,
                            },
                            "refundPolicy": {
                                "isRefundable": False,
                                "isRefundableByObt": False,
                                "isCat16": False,
                                "isConditional": False,
                            },
                            "taxBreakdown": {
                                "tax": [
                                    {
                                        "amount": {
                                            "amount": 5.2,
                                            "currencyCode": "USD",
                                            "convertedAmount": 5.2,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "taxCode": "ZP",
                                    },
                                    {
                                        "amount": {
                                            "amount": 5.6,
                                            "currencyCode": "USD",
                                            "convertedAmount": 5.6,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "taxCode": "AY",
                                    },
                                    {
                                        "amount": {
                                            "amount": 7.2,
                                            "currencyCode": "USD",
                                            "convertedAmount": 7.2,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "taxCode": "US",
                                    },
                                    {
                                        "amount": {
                                            "amount": 4.5,
                                            "currencyCode": "USD",
                                            "convertedAmount": 4.5,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "taxCode": "XF",
                                    },
                                ]
                            },
                            "commission": {
                                "amount": {
                                    "amount": 0,
                                    "currencyCode": "USD",
                                    "convertedAmount": 0,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "percent": 0,
                            },
                            "iataNumber": "14640990",
                            "fareCalculation": "SEA DL SFO Q0.17 95.81USD95.98END ZPSEA XFSEA4.5",
                            "paymentDetails": [
                                {
                                    "amount": {
                                        "base": {
                                            "amount": 95.98,
                                            "currencyCode": "USD",
                                            "convertedAmount": 95.98,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "tax": {
                                            "amount": 22.5,
                                            "currencyCode": "USD",
                                            "convertedAmount": 22.5,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                    },
                                    "fop": {
                                        "type": "CARD",
                                        "card": {
                                            "id": "9cd9b509-717a-45bd-a321-5ed089436590",
                                            "type": "CREDIT",
                                            "company": "AMEX",
                                            "name": "Chengxuan Wang",
                                            "address": {
                                                "addressLines": ["3783 Monterey Ct N"],
                                                "administrativeArea": "",
                                                "administrativeAreaName": "",
                                                "description": "",
                                                "isDefault": False,
                                                "languageCode": "",
                                                "locality": "",
                                                "locationCode": "",
                                                "organization": "",
                                                "postalCode": "98057",
                                                "continentCode": "",
                                                "recipients": [],
                                                "regionCode": "US",
                                                "regionName": "",
                                                "revision": 0,
                                                "sortingCode": "",
                                                "sublocality": "",
                                                "timezone": "",
                                            },
                                            "number": "3XXXXXXXXXXX0005",
                                            "expiryMonth": 1,
                                            "expiryYear": 2030,
                                            "cvv": "",
                                            "label": "OTTO credit card",
                                            "currency": "",
                                            "externalId": "",
                                        },
                                        "additionalInfo": "",
                                        "accessType": {
                                            "accessType": "PERSONAL",
                                            "entityIds": ["ff91d58d-60f7-45a0-ad2e-cbf26bb72676"],
                                            "entities": [
                                                {
                                                    "entityId": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676",
                                                    "centralCardAccessLevel": "UNKNOWN",
                                                }
                                            ],
                                        },
                                        "paymentMethod": "CREDIT_CARD",
                                    },
                                    "isRefunded": False,
                                }
                            ],
                            "ticketSettlement": "ARC_TICKET",
                            "publishedFare": {
                                "base": {
                                    "amount": 95.98,
                                    "currencyCode": "USD",
                                    "convertedAmount": 95.98,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "tax": {
                                    "amount": 22.5,
                                    "currencyCode": "USD",
                                    "convertedAmount": 22.5,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                            },
                            "vendorCancellationId": "",
                            "conjunctionTicketSuffix": [],
                            "pcc": "1ZSK",
                        }
                    ],
                    "boardingPass": [],
                    "booking": {
                        "seats": [
                            {"legIdx": 0, "flightIdx": 0, "number": "21A", "status": "CONFIRMED", "sourceStatus": "KK"}
                        ],
                        "luggageDetails": [],
                        "otherAncillaries": [],
                        "itinerary": {
                            "totalFare": {
                                "base": {
                                    "amount": 95.98,
                                    "currencyCode": "USD",
                                    "convertedAmount": 95.98,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "tax": {
                                    "amount": 22.5,
                                    "currencyCode": "USD",
                                    "convertedAmount": 22.5,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                            },
                            "totalFlightsFare": {
                                "base": {
                                    "amount": 95.98,
                                    "currencyCode": "USD",
                                    "convertedAmount": 95.98,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                                "tax": {
                                    "amount": 22.5,
                                    "currencyCode": "USD",
                                    "convertedAmount": 22.5,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": [],
                                },
                            },
                            "flightFareBreakup": [
                                {
                                    "legIndices": [0],
                                    "flightsFare": {
                                        "base": {
                                            "amount": 95.98,
                                            "currencyCode": "USD",
                                            "convertedAmount": 95.98,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                        "tax": {
                                            "amount": 22.5,
                                            "currencyCode": "USD",
                                            "convertedAmount": 22.5,
                                            "convertedCurrency": "USD",
                                            "otherCoinage": [],
                                        },
                                    },
                                }
                            ],
                            "fareComponents": [
                                {
                                    "fareBasisCode": "VAVNA0ME",
                                    "tourCode": "",
                                    "ticketDesignator": "",
                                    "baseFare": {
                                        "amount": 95.81,
                                        "currencyCode": "USD",
                                        "convertedAmount": 95.81,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "flightIds": [{"legIdx": 0, "flightIdx": 0}],
                                }
                            ],
                            "otherAncillaryFares": [],
                        },
                        "otherCharges": [],
                    },
                    "appliedCredits": [],
                    "specialServiceRequestInfos": [],
                }
            ],
            "automatedCancellationInfo": {
                "supportedCancellations": [
                    {
                        "cancelType": "VOID",
                        "maxCancellationDateTime": {"iso8601": "2025-03-13T04:59:00Z"},
                        "totalFare": {
                            "base": {
                                "amount": 95.98,
                                "currencyCode": "USD",
                                "convertedAmount": 95.98,
                                "convertedCurrency": "USD",
                                "otherCoinage": [],
                            },
                            "tax": {
                                "amount": 22.5,
                                "currencyCode": "USD",
                                "convertedAmount": 22.5,
                                "convertedCurrency": "USD",
                                "otherCoinage": [],
                            },
                        },
                        "refund": {
                            "amount": 118.48,
                            "currencyCode": "USD",
                            "convertedAmount": 118.48,
                            "convertedCurrency": "USD",
                            "otherCoinage": [],
                        },
                    }
                ]
            },
            "automatedExchangeInfo": {"supportedExchanges": [{"legInfos": [{"legIdx": 0}]}]},
            "bookingMetadata": {
                "fareStatistics": {
                    "statisticsItems": [
                        {
                            "statisticType": "MINIMUM",
                            "totalFare": {
                                "base": {
                                    "amount": 63.26,
                                    "currencyCode": "USD",
                                    "convertedAmount": 63.26,
                                    "convertedCurrency": "USD",
                                },
                                "tax": {
                                    "amount": 27.04,
                                    "currencyCode": "USD",
                                    "convertedAmount": 27.04,
                                    "convertedCurrency": "USD",
                                },
                            },
                        },
                        {
                            "statisticType": "MEDIAN",
                            "totalFare": {
                                "base": {
                                    "amount": 312.73,
                                    "currencyCode": "USD",
                                    "convertedAmount": 312.73,
                                    "convertedCurrency": "USD",
                                },
                                "tax": {
                                    "amount": 53.85,
                                    "currencyCode": "USD",
                                    "convertedAmount": 53.85,
                                    "convertedCurrency": "USD",
                                },
                            },
                        },
                        {
                            "statisticType": "MAXIMUM",
                            "totalFare": {
                                "base": {
                                    "amount": 3287.61,
                                    "currencyCode": "USD",
                                    "convertedAmount": 3287.61,
                                    "convertedCurrency": "USD",
                                },
                                "tax": {
                                    "amount": 276.77,
                                    "currencyCode": "USD",
                                    "convertedAmount": 276.77,
                                    "convertedCurrency": "USD",
                                },
                            },
                        },
                    ]
                }
            },
            "otherServiceInfos": [],
            "disruptedFlightDetails": [
                {
                    "departureDateTime": {"iso8601": "2025-05-30T21:55:00"},
                    "arrivalDateTime": {"iso8601": "2025-05-31T00:09:00"},
                    "cabin": "ECONOMY",
                    "originAirportCode": "SEA",
                    "destinationAirportCode": "SFO",
                    "marketing": {"num": "2635"},
                    "operating": {"num": "2635"},
                }
            ],
        },
        "additionalMetadata": {
            "airportInfo": [
                {
                    "airportCode": "SFO",
                    "airportName": "San Francisco International Airport",
                    "cityName": "San Francisco",
                    "countryName": "United States",
                    "countryCode": "US",
                    "zoneName": "America/Los_Angeles",
                    "stateCode": "CA",
                },
                {
                    "airportCode": "SEA",
                    "airportName": "Seattle–Tacoma International Airport",
                    "cityName": "Seattle",
                    "countryName": "United States",
                    "countryCode": "US",
                    "zoneName": "America/Los_Angeles",
                    "stateCode": "WA",
                },
            ],
            "airlineInfo": [{"airlineCode": "DL", "airlineName": "Delta Air Lines"}],
        },
        "preBookAnswers": {"answers": []},
        "customFields": [],
        "customFieldsV3Responses": [],
        "bookingHistory": [
            {
                "bookerInfo": {
                    "name": "OttoTest ApiUser",
                    "email": "<EMAIL>",
                    "role": "AGENT",
                    "tmcName": "",
                },
                "bookingInfo": {
                    "updatedDateTime": {"iso8601": "2025-03-11T21:19:39Z"},
                    "status": "BOOKED",
                    "bookingSourceClient": "WEB",
                },
            }
        ],
        "totalFare": {"amount": 118.48, "currencyCode": "USD", "convertedAmount": 118.48, "convertedCurrency": "USD"},
        "serviceFees": [],
        "paymentInfo": [
            {
                "fop": {
                    "type": "CARD",
                    "card": {
                        "id": "9cd9b509-717a-45bd-a321-5ed089436590",
                        "type": "CREDIT",
                        "company": "AMEX",
                        "name": "Chengxuan Wang",
                        "address": {
                            "addressLines": ["3783 Monterey Ct N"],
                            "administrativeArea": "",
                            "administrativeAreaName": "",
                            "description": "",
                            "isDefault": False,
                            "languageCode": "",
                            "locality": "",
                            "locationCode": "",
                            "organization": "",
                            "postalCode": "98057",
                            "continentCode": "",
                            "recipients": [],
                            "regionCode": "US",
                            "regionName": "",
                            "revision": 0,
                            "sortingCode": "",
                            "sublocality": "",
                            "timezone": "",
                        },
                        "number": "3XXXXXXXXXXX0005",
                        "expiryMonth": 1,
                        "expiryYear": 2030,
                        "cvv": "",
                        "label": "OTTO credit card",
                        "currency": "",
                        "externalId": "",
                    },
                    "additionalInfo": "",
                    "accessType": {
                        "accessType": "PERSONAL",
                        "entityIds": ["ff91d58d-60f7-45a0-ad2e-cbf26bb72676"],
                        "entities": [
                            {"entityId": "ff91d58d-60f7-45a0-ad2e-cbf26bb72676", "centralCardAccessLevel": "UNKNOWN"}
                        ],
                    },
                    "paymentMethod": "CREDIT_CARD",
                },
                "totalCharge": {
                    "amount": 118.48,
                    "currencyCode": "USD",
                    "convertedAmount": 118.48,
                    "convertedCurrency": "USD",
                    "otherCoinage": [],
                },
                "totalRefund": {
                    "amount": 0,
                    "currencyCode": "USD",
                    "convertedAmount": 0,
                    "convertedCurrency": "USD",
                    "otherCoinage": [],
                },
                "netCharge": {
                    "amount": 118.48,
                    "currencyCode": "USD",
                    "convertedAmount": 118.48,
                    "convertedCurrency": "USD",
                    "otherCoinage": [],
                },
            }
        ],
        "bookingStatus": "CONFIRMED_STATUS",
        "contactSupport": False,
        "travelerPnrVisibilityStatus": "VISIBLE",
        "pnrCreationDetails": {},
        "approvalInfo": [{"approvalStatus": "APPROVAL_NOT_REQUIRED", "approvalType": "APPROVAL_TYPE_UNKNOWN"}],
        "tripId": "7695338655",
        "documents": [],
        "pnrId": "4394753423",
        "invoiceInfos": [
            {
                "invoiceNumber": "SPOT-US-001847",
                "productType": "PNR",
                "invoiceId": "d676b78e-1f21-430c-9e53-51094b5db25a",
            }
        ],
        "totalFareAmount": {
            "base": {"amount": 95.98, "currencyCode": "USD", "convertedAmount": 95.98, "convertedCurrency": "USD"},
            "tax": {"amount": 22.5, "currencyCode": "USD", "convertedAmount": 22.5, "convertedCurrency": "USD"},
        },
        "dkNumber": "0000002558",
        "savingsFare": {
            "fareAmount": {
                "base": {"amount": 95.98, "currencyCode": "USD", "convertedAmount": 95.98, "convertedCurrency": "USD"},
                "tax": {"amount": 22.5, "currencyCode": "USD", "convertedAmount": 22.5, "convertedCurrency": "USD"},
            },
            "isTaxIncluded": True,
        },
        "tripUsageMetadata": {"tripUsageType": "STANDARD"},
        "owningPccInfo": {"zoneId": "America/Chicago"},
    },
    "operationSummary": {"pnrUpdateSummary": {"ticketsIssued": ["0067173776555"]}},
}


@pytest.mark.asyncio
@pytest.mark.manual
async def test_process_exchange_webhook_event():
    data = SpotnanaTravelDelivery(**exchange_event)

    async def test(d: SpotnanaTravelDelivery):
        print(d)

    await handle_spotnana_webhook_event(data, test)


@pytest.mark.asyncio
@pytest.mark.manual
async def test_process_cancel_event():
    data = SpotnanaTravelDelivery(**cancel_event)

    async def test(d: SpotnanaTravelDelivery):
        print(d)

    await handle_spotnana_webhook_event(data, test)


@pytest.mark.asyncio
@pytest.mark.manual
async def test_process_booked_event():
    data = SpotnanaTravelDelivery(**booked_event)

    async def test(d: SpotnanaTravelDelivery):
        print(d)

    await handle_spotnana_webhook_event(data, test)


@pytest.mark.asyncio
@pytest.mark.manual
async def test_process_in_trip_minor_change_event():
    json_response = {}
    # Read JSON response from file
    with open("./tests/data/spotnana_webhook_event_minor_change.json", "r") as file:
        json_response = file.read()
    event = json.loads(json_response)
    data = SpotnanaTravelDelivery(**event)
    data.payload["tripId"] = "6742858712"

    async def test(d: SpotnanaTravelDelivery):
        print(d)

    await handle_spotnana_webhook_event(data, test)


@pytest.mark.asyncio
@pytest.mark.manual
async def test_process_in_trip_critical_change_event():
    json_response = {}
    # Read JSON response from file
    with open("./tests/data/spotnana_webhook_event_critical_change.json", "r") as file:
        json_response = file.read()
    event = json.loads(json_response)
    data = SpotnanaTravelDelivery(**event)
    data.payload["tripId"] = "6742858712"

    async def test(d: SpotnanaTravelDelivery):
        print(d)

    await handle_spotnana_webhook_event(data, test)
