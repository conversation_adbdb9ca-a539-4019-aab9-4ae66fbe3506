"""
Unit tests for InTripAgent flight webhook field change tracking.
Tests the new meaningful field change tracking functionality.
"""

import json
import os
from unittest.mock import Mock

from in_trip.in_trip import Field<PERSON>hange, InTripAgent, InTripFlightBooking, InTripFlightInfo
from server.database.models.chat_thread import ChatThread
from server.schemas.authenticate.user import User


class TestInTripFieldChanges:
    """Test suite for InTripAgent field change tracking functionality."""

    def _load_webhook_legs_from_json(self):
        """Load webhook legs data from Flight_schedule_change_closed_example.json."""
        json_file_path = os.path.join(
            os.path.dirname(__file__), "..", "in_trip", "Flight_schedule_change_closed_example.json"
        )
        with open(json_file_path, "r") as f:
            data = json.load(f)
        return data["payload"]["airPnr"]["legs"]

    def setup_method(self):
        """Setup test fixtures."""
        # Create mock user and thread
        self.user = Mock(spec=User)
        self.user.id = "test-user-123"
        self.user.email = "<EMAIL>"

        self.thread = Mock(spec=ChatThread)
        self.thread.id = "test-thread-123"

        # Create InTripAgent instance
        self.agent = InTripAgent(self.user, self.thread)

        # Load webhook data from JSON file
        self.webhook_legs = self._load_webhook_legs_from_json()

        # Sample flight booking data
        self.sample_flight_booking = InTripFlightBooking(
            legs=[
                InTripFlightInfo(
                    leg_index=0,
                    confirmation_number="ABC123",
                    departure_time="2025-04-03T05:00:00",  # Different from JSON to test change detection
                    departure_timezone="America/Los_Angeles",
                    arrival_time="2025-04-03T08:00:00",  # Different from JSON to test change detection
                    arrival_timezone="America/Los_Angeles",
                    airline="AA",
                    flight_numbers=["6420"],
                    origin_name="Seattle–Tacoma International Airport",
                    origin_code="SEA",
                    destination_name="Los Angeles International Airport",
                    destination_code="LAX",
                    price_string="$243.30",
                    seat="18C",
                    gate="A5",
                    terminal="North",
                    current_status="on_time",
                )
            ]
        )

    def test_compare_and_update_flight_legs_departure_time_change(self):
        """Test detection and update of departure time changes."""
        # Use webhook data from JSON file
        webhook_legs = self.webhook_legs

        # Execute the function
        field_changes = self.agent._compare_and_update_flight_legs(self.sample_flight_booking, webhook_legs)

        # Verify results - should detect changes between sample data and JSON data
        assert len(field_changes) >= 1  # At least departure time should change

        # Find the departure time change
        departure_changes = [c for c in field_changes if c.field_name == "departure_time"]
        assert len(departure_changes) == 1

        departure_change = departure_changes[0]
        assert departure_change.old_value == "2025-04-03T05:00:00"  # Original sample value
        assert departure_change.new_value == "2025-04-03T06:10:00"  # Value from JSON

        # Verify leg was updated
        assert self.sample_flight_booking.legs[0].departure_time == "2025-04-03T06:10:00"

        # Verify aggregate fields were repopulated
        assert self.sample_flight_booking.departure_time == "2025-04-03T06:10:00"

    def test_compare_and_update_flight_legs_multiple_changes(self):
        """Test detection of multiple field changes in one update."""
        # Use webhook data from JSON file
        webhook_legs = self.webhook_legs

        field_changes = self.agent._compare_and_update_flight_legs(self.sample_flight_booking, webhook_legs)

        # Should detect multiple changes between sample data and JSON data
        assert len(field_changes) >= 2  # At least departure and arrival time should change

        # Check that we detect time changes
        field_names = [change.field_name for change in field_changes]
        assert "departure_time" in field_names
        assert "arrival_time" in field_names

        # Verify specific changes
        departure_change = next(c for c in field_changes if c.field_name == "departure_time")
        assert departure_change.old_value == "2025-04-03T05:00:00"  # Original sample value
        assert departure_change.new_value == "2025-04-03T06:10:00"  # Value from JSON

        arrival_change = next(c for c in field_changes if c.field_name == "arrival_time")
        assert arrival_change.old_value == "2025-04-03T08:00:00"  # Original sample value
        assert arrival_change.new_value == "2025-04-03T08:59:00"  # Value from JSON

    def test_compare_and_update_flight_legs_no_changes(self):
        """Test when webhook data matches current data (no changes)."""
        # First update the sample booking to match the JSON data
        self.sample_flight_booking.legs[0].departure_time = "2025-04-03T06:10:00"
        self.sample_flight_booking.legs[0].arrival_time = "2025-04-03T08:59:00"
        self.sample_flight_booking.legs[0].gate = ""  # JSON has empty gate
        self.sample_flight_booking.legs[0].terminal = ""  # JSON has empty terminal

        # Use webhook data from JSON file
        webhook_legs = self.webhook_legs

        field_changes = self.agent._compare_and_update_flight_legs(self.sample_flight_booking, webhook_legs)

        # Should detect no changes since we made the sample data match the JSON
        assert len(field_changes) == 0

    def test_compare_and_update_flight_legs_partial_webhook_data(self):
        """Test handling of partial webhook data (missing fields)."""
        # Create partial webhook data based on JSON but with only departure time
        webhook_legs = [
            {
                "flights": [
                    {
                        "departureDateTime": {"iso8601": "2025-04-03T06:10:00"},  # From JSON
                        # Missing arrival time, gate, terminal, status
                    }
                ]
            }
        ]

        field_changes = self.agent._compare_and_update_flight_legs(self.sample_flight_booking, webhook_legs)

        # Should detect departure time change and possibly status change (due to time-based fallback)
        assert len(field_changes) >= 1

        # Find the departure time change
        departure_changes = [c for c in field_changes if c.field_name == "departure_time"]
        assert len(departure_changes) == 1
        assert departure_changes[0].old_value == "2025-04-03T05:00:00"  # Original sample value
        assert departure_changes[0].new_value == "2025-04-03T06:10:00"  # Value from partial webhook

    def test_compare_and_update_flight_legs_with_new_fields(self):
        """Test handling of new fields that weren't previously set."""
        # Start with a leg that has no gate/terminal and matches JSON times
        self.sample_flight_booking.legs[0].gate = None
        self.sample_flight_booking.legs[0].terminal = None
        self.sample_flight_booking.legs[0].departure_time = "2025-04-03T06:10:00"  # Match JSON
        self.sample_flight_booking.legs[0].arrival_time = "2025-04-03T08:59:00"  # Match JSON

        # Create webhook data with gate/terminal info (JSON has empty gate/terminal, so we'll add some)
        webhook_legs = [
            {
                "flights": [
                    {
                        "departureDateTime": {"iso8601": "2025-04-03T06:10:00"},  # Same as sample
                        "arrivalDateTime": {"iso8601": "2025-04-03T08:59:00"},  # Same as sample
                        "departureGate": {"gate": "B5", "terminal": "Terminal 1"},  # New values
                        "sourceStatus": "HK",  # Same
                    }
                ]
            }
        ]

        field_changes = self.agent._compare_and_update_flight_legs(self.sample_flight_booking, webhook_legs)

        # Should detect gate and terminal changes
        assert len(field_changes) == 2
        field_names = [change.field_name for change in field_changes]
        assert "gate" in field_names
        assert "terminal" in field_names

        # Verify new values were set
        assert self.sample_flight_booking.legs[0].gate == "B5"
        assert self.sample_flight_booking.legs[0].terminal == "Terminal 1"

    def test_field_change_meaningful_names(self):
        """Test that field changes use meaningful, user-friendly names."""
        # Use webhook data from JSON file
        webhook_legs = self.webhook_legs

        field_changes = self.agent._compare_and_update_flight_legs(self.sample_flight_booking, webhook_legs)

        # Check that field names are user-meaningful
        field_names = [change.field_name for change in field_changes]

        # Should NOT have technical names like "legs[0].departure_time"
        assert not any("legs[" in name for name in field_names)

        # Should have meaningful names (at least departure_time and arrival_time should change)
        assert "departure_time" in field_names
        assert "arrival_time" in field_names

        # All field names should be meaningful
        meaningful_names = {"departure_time", "arrival_time", "gate", "terminal", "flight_status"}
        assert all(name in meaningful_names for name in field_names)

    def test_field_change_actual_values(self):
        """Test that field changes capture actual before/after values."""
        # Use webhook data from JSON file
        webhook_legs = self.webhook_legs

        field_changes = self.agent._compare_and_update_flight_legs(self.sample_flight_booking, webhook_legs)

        # Check departure time change
        departure_change = next(c for c in field_changes if c.field_name == "departure_time")
        assert departure_change.old_value == "2025-04-03T05:00:00"  # Original sample value
        assert departure_change.new_value == "2025-04-03T06:10:00"  # Value from JSON

        # Check arrival time change
        arrival_change = next(c for c in field_changes if c.field_name == "arrival_time")
        assert arrival_change.old_value == "2025-04-03T08:00:00"  # Original sample value
        assert arrival_change.new_value == "2025-04-03T08:59:00"  # Value from JSON

    def test_build_flight_change_summary_with_new_field_names(self):
        """Test that change summary works with new meaningful field names."""
        field_changes = [
            FieldChange(field_name="departure_time", old_value="2025-04-03T05:00:00", new_value="2025-04-03T06:10:00"),
            FieldChange(field_name="gate", old_value="A5", new_value="B10"),
        ]

        summary = self.agent._build_flight_change_summary(field_changes)

        # Should generate readable summary
        assert "Departure" in summary
        assert "Gate changed from A5 to B10" in summary

    def test_determine_change_type_with_new_field_names(self):
        """Test that change type determination works with new field names."""
        # Test schedule change
        schedule_changes = [FieldChange(field_name="departure_time", old_value="old", new_value="new")]
        assert self.agent._determine_change_type_from_changes(schedule_changes) == "schedule"

        # Test gate change
        gate_changes = [FieldChange(field_name="gate", old_value="A5", new_value="B10")]
        assert self.agent._determine_change_type_from_changes(gate_changes) == "gate"

        # Test status change
        status_changes = [FieldChange(field_name="flight_status", old_value="on_time", new_value="delayed")]
        assert self.agent._determine_change_type_from_changes(status_changes) == "status"

    def test_normalize_flight_status_common_codes(self):
        """Test normalization of common airline status codes."""
        flight_booking = self.sample_flight_booking
        sample_leg = flight_booking.legs[0]

        # Test confirmed/OK statuses
        assert flight_booking._normalize_flight_status("HK", sample_leg) == "on_time"
        assert flight_booking._normalize_flight_status("OK", sample_leg) == "on_time"
        assert flight_booking._normalize_flight_status("RR", sample_leg) == "on_time"

        # Test boarding/departure statuses
        assert flight_booking._normalize_flight_status("BD", sample_leg) == "boarding"
        assert flight_booking._normalize_flight_status("DP", sample_leg) == "departed"
        assert flight_booking._normalize_flight_status("CL", sample_leg) == "departed"

        # Test delay statuses
        assert flight_booking._normalize_flight_status("DL", sample_leg) == "delayed"
        assert flight_booking._normalize_flight_status("RT", sample_leg) == "delayed"

        # Test cancellation statuses
        assert flight_booking._normalize_flight_status("XX", sample_leg) == "cancelled"
        assert flight_booking._normalize_flight_status("CN", sample_leg) == "cancelled"
        assert flight_booking._normalize_flight_status("NO", sample_leg) == "cancelled"

    def test_normalize_flight_status_case_insensitive(self):
        """Test that status normalization is case insensitive."""
        flight_booking = self.sample_flight_booking
        sample_leg = flight_booking.legs[0]

        # Test lowercase
        assert flight_booking._normalize_flight_status("hk", sample_leg) == "on_time"
        assert flight_booking._normalize_flight_status("dl", sample_leg) == "delayed"

        # Test mixed case
        assert flight_booking._normalize_flight_status("Hk", sample_leg) == "on_time"
        assert flight_booking._normalize_flight_status("Dl", sample_leg) == "delayed"

    def test_normalize_flight_status_keyword_matching(self):
        """Test status normalization with keyword matching for complex codes."""
        flight_booking = self.sample_flight_booking
        sample_leg = flight_booking.legs[0]

        # Test keyword matching
        assert flight_booking._normalize_flight_status("CANCELLED_BY_AIRLINE", sample_leg) == "cancelled"
        assert flight_booking._normalize_flight_status("DELAYED_WEATHER", sample_leg) == "delayed"
        assert flight_booking._normalize_flight_status("BOARDING_STARTED", sample_leg) == "boarding"
        assert flight_booking._normalize_flight_status("CONFIRMED_SEAT", sample_leg) == "on_time"

    def test_normalize_flight_status_unknown_codes(self):
        """Test handling of unknown status codes with fallback logic."""
        flight_booking = self.sample_flight_booking
        sample_leg = flight_booking.legs[0]

        # Test unknown codes fall back to time-based logic or default
        unknown_status = flight_booking._normalize_flight_status("UNKNOWN_CODE", sample_leg)
        assert unknown_status in ["on_time", "boarding_soon", "departed", "scheduled"]

        # Test empty/None status
        assert flight_booking._normalize_flight_status(None, sample_leg) in [
            "on_time",
            "boarding_soon",
            "departed",
            "scheduled",
        ]
        assert flight_booking._normalize_flight_status("", sample_leg) in [
            "on_time",
            "boarding_soon",
            "departed",
            "scheduled",
        ]

    def test_status_normalization_in_flight_leg_changes(self):
        """Test that status changes are properly normalized in flight leg processing."""
        # Create webhook data with raw status code
        webhook_legs = [
            {
                "flights": [
                    {
                        "departureDateTime": {"iso8601": "2025-04-03T05:00:00"},  # Same as sample
                        "arrivalDateTime": {"iso8601": "2025-04-03T08:00:00"},  # Same as sample
                        "sourceStatus": "DL",  # Raw delayed status
                    }
                ]
            }
        ]

        # Set initial status to something different that will normalize to different value
        self.sample_flight_booking.legs[0].current_status = "on_time"

        field_changes = self.agent._compare_and_update_flight_legs(self.sample_flight_booking, webhook_legs)

        # Should detect status change and normalize it
        status_changes = [c for c in field_changes if c.field_name == "flight_status"]
        assert len(status_changes) == 1

        status_change = status_changes[0]
        assert status_change.old_value == "on_time"
        assert status_change.new_value == "delayed"  # Normalized from "DL"

        # Verify leg was updated with normalized status
        assert self.sample_flight_booking.legs[0].current_status == "delayed"
