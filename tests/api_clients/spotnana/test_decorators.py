"""Unit tests for Spotnana API decorators."""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi.exceptions import HTTPException

from server.api_clients.spotnana.decorators import (
    _log_request_to_file,
    _log_response_to_file,
    spotnana_http_logger,
)


class TestSpotnanaHttpLogger:
    """Test suite for spotnana_http_logger decorator."""

    @pytest.fixture
    def mock_client(self):
        """Create a mock client with base_url."""
        client = MagicMock()
        client.base_url = "https://api.spotnana.com"
        return client

    @pytest.mark.asyncio
    async def test_get_request_logging(self, mock_client):
        """Test GET request logging with query parameters."""

        @spotnana_http_logger("GET")
        async def get_method(self, endpoint, params=None, **kwargs):
            return {"data": "response"}

        with patch("server.api_clients.spotnana.decorators.SpotnanaLoggingConfig") as mock_config:
            mock_config.should_log_to_file.return_value = True
            mock_config.should_log_to_firehose.return_value = True
            mock_config.check_spotnana_errors.return_value = None

            with patch("server.api_clients.spotnana.decorators._log_request_to_file") as mock_log_req:
                with patch("server.api_clients.spotnana.decorators._log_response_to_file") as mock_log_resp:
                    result = await get_method(mock_client, "search", params={"query": "test"})

                    assert result == {"data": "response"}
                    mock_log_req.assert_called_once()
                    mock_log_resp.assert_called_once()
                    mock_config.log_request_to_firehose.assert_called_once()

    @pytest.mark.asyncio
    async def test_post_request_logging(self, mock_client):
        """Test POST request logging with request data."""

        @spotnana_http_logger("POST")
        async def post_method(self, endpoint, data=None, **kwargs):
            return {"created": True}

        with patch("server.api_clients.spotnana.decorators.SpotnanaLoggingConfig") as mock_config:
            mock_config.should_log_to_file.return_value = True
            mock_config.should_log_to_firehose.return_value = True
            mock_config.check_spotnana_errors.return_value = None

            with patch("server.api_clients.spotnana.decorators._log_request_to_file") as mock_log_req:
                with patch("server.api_clients.spotnana.decorators._log_response_to_file") as mock_log_resp:
                    result = await post_method(mock_client, "create", data={"name": "test"})

                    assert result == {"created": True}
                    mock_log_req.assert_called_once()
                    mock_log_resp.assert_called_once()
                    mock_config.log_request_to_firehose.assert_called_once()

    @pytest.mark.asyncio
    async def test_no_logging_when_disabled(self, mock_client):
        """Test that no logging occurs when disabled."""

        @spotnana_http_logger("GET")
        async def get_method(self, endpoint, **kwargs):
            return {"data": "response"}

        with patch("server.api_clients.spotnana.decorators.SpotnanaLoggingConfig") as mock_config:
            mock_config.should_log_to_file.return_value = False
            mock_config.should_log_to_firehose.return_value = False
            mock_config.check_spotnana_errors.return_value = None

            with patch("server.api_clients.spotnana.decorators._log_request_to_file") as mock_log_req:
                with patch("server.api_clients.spotnana.decorators._log_response_to_file") as mock_log_resp:
                    result = await get_method(mock_client, "search")

                    assert result == {"data": "response"}
                    mock_log_req.assert_not_called()
                    mock_log_resp.assert_not_called()
                    mock_config.log_request_to_firehose.assert_not_called()

    @pytest.mark.asyncio
    async def test_http_exception_handling(self, mock_client):
        """Test HTTP exception handling and conversion to SpotnanaException."""

        @spotnana_http_logger("GET")
        async def get_method(self, endpoint, **kwargs):
            raise HTTPException(status_code=400, detail={"error": "Bad request"})

        with patch("server.api_clients.spotnana.decorators.SpotnanaLoggingConfig") as mock_config:
            mock_config.should_log_to_file.return_value = False
            mock_config.should_log_to_firehose.return_value = False

            with patch("server.api_clients.spotnana.exceptions.SpotnanaException") as mock_exception:
                mock_exception.create_from_status_code.return_value = Exception("Converted exception")

                with pytest.raises(Exception, match="Converted exception"):
                    await get_method(mock_client, "search")

                mock_exception.create_from_status_code.assert_called_once_with(
                    status_code=400, response_data={"error": "Bad request"}
                )

    @pytest.mark.asyncio
    async def test_other_exception_handling(self, mock_client):
        """Test handling of non-HTTP exceptions."""

        @spotnana_http_logger("GET")
        async def get_method(self, endpoint, **kwargs):
            raise ValueError("Test error")

        with patch("server.api_clients.spotnana.decorators.SpotnanaLoggingConfig") as mock_config:
            mock_config.should_log_to_file.return_value = False
            mock_config.should_log_to_firehose.return_value = False

            with patch("structlog.get_logger") as mock_logger:
                mock_log = MagicMock()
                mock_logger.return_value = mock_log

                with pytest.raises(ValueError, match="Test error"):
                    await get_method(mock_client, "search")

                mock_log.error.assert_called_once()

    @pytest.mark.asyncio
    async def test_spotnana_error_checking(self, mock_client):
        """Test Spotnana error checking in response."""

        @spotnana_http_logger("GET")
        async def get_method(self, endpoint, **kwargs):
            return {"errorMessages": [{"errorCode": "TEST_ERROR"}]}

        with patch("server.api_clients.spotnana.decorators.SpotnanaLoggingConfig") as mock_config:
            mock_config.should_log_to_file.return_value = False
            mock_config.should_log_to_firehose.return_value = False
            mock_config.check_spotnana_errors.side_effect = Exception("Spotnana error")

            with pytest.raises(Exception, match="Spotnana error"):
                await get_method(mock_client, "search")


class TestLogRequestToFile:
    """Test suite for _log_request_to_file function."""

    @pytest.mark.asyncio
    async def test_log_request_to_file_success(self):
        """Test successful request logging to file."""
        with patch("server.api_clients.spotnana.decorators.SpotnanaLoggingConfig") as mock_config:
            mock_config.generate_log_file_path.return_value = "/test/path/request.json"
            mock_config.extract_context_variables.return_value = {"trip_id": "123"}
            mock_config.write_log_file = AsyncMock()

            with patch("asyncio.create_task") as mock_task:
                await _log_request_to_file("https://api.test.com/search", {"query": "test"}, "GET")

                mock_task.assert_called_once()
                mock_config.generate_log_file_path.assert_called_once_with("https://api.test.com/search", "request")
                mock_config.extract_context_variables.assert_called_once()

    @pytest.mark.asyncio
    async def test_log_request_to_file_exception(self):
        """Test error handling in request logging."""
        with patch("server.api_clients.spotnana.decorators.SpotnanaLoggingConfig") as mock_config:
            mock_config.generate_log_file_path.side_effect = Exception("File error")

            with patch("structlog.get_logger") as mock_logger:
                mock_log = MagicMock()
                mock_logger.return_value = mock_log

                await _log_request_to_file("https://api.test.com/search", {"query": "test"}, "GET")

                mock_log.error.assert_called_once()
                assert "Failed to log request to file" in mock_log.error.call_args[0][0]


class TestLogResponseToFile:
    """Test suite for _log_response_to_file function."""

    @pytest.mark.asyncio
    async def test_log_response_to_file_success(self):
        """Test successful response logging to file."""
        with patch("server.api_clients.spotnana.decorators.SpotnanaLoggingConfig") as mock_config:
            mock_config.generate_log_file_path.return_value = "/test/path/response.json"
            mock_config.extract_context_variables.return_value = {"trip_id": "123"}
            mock_config.write_log_file = AsyncMock()

            with patch("asyncio.create_task") as mock_task:
                await _log_response_to_file("https://api.test.com/search", {"results": []})

                mock_task.assert_called_once()
                mock_config.generate_log_file_path.assert_called_once_with("https://api.test.com/search", "response")
                mock_config.extract_context_variables.assert_called_once()

    @pytest.mark.asyncio
    async def test_log_response_to_file_exception(self):
        """Test error handling in response logging."""
        with patch("server.api_clients.spotnana.decorators.SpotnanaLoggingConfig") as mock_config:
            mock_config.generate_log_file_path.side_effect = Exception("File error")

            with patch("structlog.get_logger") as mock_logger:
                mock_log = MagicMock()
                mock_logger.return_value = mock_log

                await _log_response_to_file("https://api.test.com/search", {"results": []})

                mock_log.error.assert_called_once()
                assert "Failed to log response to file" in mock_log.error.call_args[0][0]
