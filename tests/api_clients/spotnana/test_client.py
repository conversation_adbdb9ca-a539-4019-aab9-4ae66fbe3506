"""
Tests for the core Spotnana client authentication and base functionality.
"""

import asyncio
import time
from datetime import datetime, timedelta
from unittest.mock import patch

import pytest
from fastapi.exceptions import HTTPException

from server.api_clients.spotnana.client import SpotnanaClient
from server.api_clients.spotnana.exceptions import (
    SpotnanaAuthenticationError,
    SpotnanaException,
    SpotnanaRateLimitError,
    SpotnanaServerError,
)
from server.api_clients.spotnana.models import TokenInfo


class TestSpotnanaClient:
    """Test suite for SpotnanaClient authentication and base functionality."""

    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        with patch("server.api_clients.spotnana.client.settings") as mock_settings:
            mock_settings.SPOTNANA_HOST = "https://api.spotnana.com"
            mock_settings.SPOTNANA_CLIENT_ID = "test_client_id"
            mock_settings.SPOTNANA_CLIENT_SECRET = "test_client_secret"
            mock_settings.SPOTNANA_COMPANY_GUID = "test_company_guid"
            mock_settings.SPOTNANA_API_KEY = "test_api_key"
            yield mock_settings

    @pytest.fixture
    def client(self, mock_settings):
        """Create a SpotnanaClient instance for testing."""
        return SpotnanaClient()

    @pytest.fixture
    def mock_auth_response(self):
        """Mock authentication response."""
        return {"token": "test_token_123", "expiryTimeInSeconds": 3600, "tokenType": "Bearer"}

    @pytest.mark.asyncio
    async def test_client_initialization(self, mock_settings):
        """Test that client initializes correctly with settings."""
        client = SpotnanaClient()

        assert client.base_url == "https://api.spotnana.com"
        assert client.client_id == "test_client_id"
        assert client.client_secret == "test_client_secret"
        assert client.company_id == "test_company_guid"
        assert client.api_key == "test_api_key"
        assert client._token_info is None
        assert not client.is_authenticated

    @pytest.mark.asyncio
    async def test_client_initialization_missing_config(self):
        """Test that client raises exception with missing configuration."""
        with patch("server.api_clients.spotnana.client.settings") as mock_settings:
            mock_settings.SPOTNANA_HOST = None
            mock_settings.SPOTNANA_CLIENT_ID = "test_client_id"
            mock_settings.SPOTNANA_CLIENT_SECRET = "test_client_secret"

            with pytest.raises(SpotnanaException, match="SPOTNANA_HOST is not set"):
                SpotnanaClient()

    @pytest.mark.asyncio
    async def test_authenticate_success(self, client, mock_auth_response):
        """Test successful authentication."""
        with patch("server.api_clients.spotnana.client.make_post_request") as mock_post:
            mock_post.return_value = mock_auth_response

            await client.authenticate()

            assert client._token_info is not None
            assert client._token_info.token == "test_token_123"
            assert client.is_authenticated

            # Verify request was made with correct parameters
            mock_post.assert_called_once()
            args, kwargs = mock_post.call_args
            assert args[0] == "https://api.spotnana.com/get-auth-token"
            assert kwargs["headers"]["Content-Type"] == "application/json"
            assert kwargs["data"]["clientId"] == "test_client_id"
            assert kwargs["data"]["clientSecret"] == "test_client_secret"

    @pytest.mark.asyncio
    async def test_authenticate_failure(self, client):
        """Test authentication failure."""
        with patch("server.api_clients.spotnana.client.make_post_request") as mock_post:
            mock_post.side_effect = Exception("Network error")

            with pytest.raises(SpotnanaException, match="Authentication failed"):
                await client.authenticate()

            assert client._token_info is None
            assert not client.is_authenticated

    @pytest.mark.asyncio
    async def test_authenticate_concurrent_calls(self, client, mock_auth_response):
        """Test that concurrent authentication calls don't cause issues."""
        with patch("server.api_clients.spotnana.client.make_post_request") as mock_post:
            mock_post.return_value = mock_auth_response

            # Make multiple concurrent authentication calls
            await asyncio.gather(client.authenticate(), client.authenticate(), client.authenticate())

            # Should only make one actual request due to async lock
            assert mock_post.call_count <= 1  # Could be 0 if token is still valid
            assert client.is_authenticated

    @pytest.mark.asyncio
    async def test_is_token_expired(self, client):
        """Test token expiration logic."""
        # No token should be considered expired
        assert client._is_token_expired() is True

        # Valid token should not be expired
        future_time = datetime.now() + timedelta(minutes=30)
        client._token_info = TokenInfo(token="test_token", expire=future_time.timestamp(), expires_at=future_time)
        assert client._is_token_expired() is False

        # Expired token should be considered expired
        past_time = datetime.now() - timedelta(minutes=30)
        client._token_info = TokenInfo(token="test_token", expire=past_time.timestamp(), expires_at=past_time)
        assert client._is_token_expired() is True

    @pytest.mark.asyncio
    async def test_token_expiration_buffer(self, client):
        """Test that token expiration includes a 30-second buffer."""
        # Token expiring in 20 seconds should be considered expired (due to 30s buffer)
        near_future = datetime.now() + timedelta(seconds=20)
        client._token_info = TokenInfo(token="test_token", expire=near_future.timestamp(), expires_at=near_future)
        assert client._is_token_expired() is True

    @pytest.mark.asyncio
    async def test_get_auth_headers(self, client, mock_auth_response):
        """Test getting authenticated headers."""
        with patch("server.api_clients.spotnana.client.make_post_request") as mock_post:
            mock_post.return_value = mock_auth_response

            headers = await client._get_auth_headers()

            assert headers["Authorization"] == "Bearer test_token_123"
            assert headers["Content-Type"] == "application/json"

    @pytest.mark.asyncio
    async def test_get_auth_headers_no_token(self, client):
        """Test getting auth headers when authentication fails."""
        with patch.object(client, "authenticate", side_effect=Exception("Auth failed")):
            with pytest.raises(Exception, match="Auth failed"):
                await client._get_auth_headers()

    @pytest.mark.asyncio
    async def test_handle_http_error_authentication(self, client):
        """Test handling HTTP authentication errors."""
        http_exception = HTTPException(status_code=401, detail="Unauthorized")

        with pytest.raises(SpotnanaAuthenticationError, match="Unauthorized"):
            client._handle_http_error(http_exception, "test_url", "GET")

    @pytest.mark.asyncio
    async def test_handle_http_error_rate_limit(self, client):
        """Test handling HTTP rate limit errors."""
        http_exception = HTTPException(status_code=429, detail="Rate limit exceeded")

        with pytest.raises(SpotnanaRateLimitError, match="Rate limit exceeded"):
            client._handle_http_error(http_exception, "test_url", "GET")

    @pytest.mark.asyncio
    async def test_handle_http_error_server_error(self, client):
        """Test handling HTTP server errors."""
        http_exception = HTTPException(status_code=500, detail="Internal server error")

        with pytest.raises(SpotnanaServerError, match="Internal server error"):
            client._handle_http_error(http_exception, "test_url", "GET")

    @pytest.mark.asyncio
    async def test_get_request(self, client, mock_auth_response):
        """Test GET request functionality."""
        with patch("server.api_clients.spotnana.client.make_post_request") as mock_post:
            mock_post.return_value = mock_auth_response

            with patch("server.api_clients.spotnana.client.make_get_request") as mock_get:
                mock_get.return_value = {"data": "test_data"}

                result = await client.get("v2/test", params={"key": "value"})

                assert result == {"data": "test_data"}
                mock_get.assert_called_once_with(
                    "https://api.spotnana.com/v2/test",
                    headers={"Authorization": "Bearer test_token_123", "Content-Type": "application/json"},
                    params={"key": "value"},
                )

    @pytest.mark.asyncio
    async def test_post_request(self, client, mock_auth_response):
        """Test POST request functionality."""
        with patch("server.api_clients.spotnana.client.make_post_request") as mock_post:
            # First call for authentication
            mock_post.return_value = mock_auth_response
            await client.authenticate()

            # Second call for actual request
            mock_post.return_value = {"data": "test_data"}

            result = await client.post("v2/test", data={"key": "value"})

            assert result == {"data": "test_data"}
            # Should have been called twice (auth + actual request)
            assert mock_post.call_count == 2

    @pytest.mark.asyncio
    async def test_put_request(self, client, mock_auth_response):
        """Test PUT request functionality."""
        with patch("server.api_clients.spotnana.client.make_post_request") as mock_post:
            mock_post.return_value = mock_auth_response

            with patch("server.api_clients.spotnana.client.make_put_request") as mock_put:
                mock_put.return_value = {"data": "test_data"}

                result = await client.put("v2/test", data={"key": "value"})

                assert result == {"data": "test_data"}
                mock_put.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_request(self, client, mock_auth_response):
        """Test DELETE request functionality."""
        with patch("server.api_clients.spotnana.client.make_post_request") as mock_post:
            mock_post.return_value = mock_auth_response

            with patch("server.api_clients.spotnana.client.make_delete_request") as mock_delete:
                mock_delete.return_value = {"data": "test_data"}

                result = await client.delete("v2/test")

                assert result == {"data": "test_data"}
                mock_delete.assert_called_once()

    @pytest.mark.asyncio
    async def test_token_info_property(self, client):
        """Test token_info property."""
        assert client.token_info is None

        token_info = TokenInfo(
            token="test_token", expire=time.time() + 3600, expires_at=datetime.now() + timedelta(hours=1)
        )
        client._token_info = token_info

        assert client.token_info == token_info

    @pytest.mark.asyncio
    async def test_is_authenticated_property(self, client):
        """Test is_authenticated property."""
        assert not client.is_authenticated

        # Set valid token
        future_time = datetime.now() + timedelta(minutes=30)
        client._token_info = TokenInfo(token="test_token", expire=future_time.timestamp(), expires_at=future_time)
        assert client.is_authenticated

        # Set expired token
        past_time = datetime.now() - timedelta(minutes=30)
        client._token_info = TokenInfo(token="test_token", expire=past_time.timestamp(), expires_at=past_time)
        assert not client.is_authenticated
