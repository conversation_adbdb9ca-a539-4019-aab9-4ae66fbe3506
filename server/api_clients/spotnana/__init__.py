"""
Spotnana API client package.

This package provides a clean, typed interface to the Spotnana API organized
by functional areas following the Spotnana OpenAPI structure.
"""

from .client import SpotnanaClient
from .exceptions import SpotnanaAuthenticationError, SpotnanaException, SpotnanaRateLimitError

__all__ = [
    "SpotnanaClient",
    "SpotnanaException",
    "SpotnanaAuthenticationError",
    "SpotnanaRateLimitError",
]
