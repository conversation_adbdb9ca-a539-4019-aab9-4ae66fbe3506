"""
Spotnana API logging configuration and utilities.

This module provides utilities for automated logging of Spotnana API requests and responses
with structured file organization and context extraction.
"""

import json
import re
from datetime import datetime
from pathlib import Path
from typing import Any
from uuid import uuid4

import aiofiles
import structlog

from front_of_house_agent import flight_utils
from server.utils.settings import settings

# Local hardcoded configuration for file logging
# Controls which endpoints should have file logging enabled
# Based on current usage of request_filename in the codebase
FILE_LOGGING_CONFIG = {
    # Enable file logging for specific endpoints that currently use request_filename
    "endpoints": [
        # User management endpoints
        "users/unknown",  # /v2/users (query_user_spotnana). endpoint is not present for this call
        # Traveler management endpoints
        "traveler/search",  # /v1/traveler/search (traveler_search_spotnana)
        "traveler/read",  # /v1/traveler/read (traveler_read_spotnana)
        # Air booking flow endpoints
        "air/selected-itinerary",  # /v2/air/selected-itinerary (get_selected_itinerary_spotnana)
        "air/flight-checkout",  # /v2/air/flight-checkout (flight_checkout_spotnana)
        "air/seat-map",  # /v2/air/seat-map (seat_map_spotnana)
        "air/modify-book",  # /v2/air/modify-book (exchange_flight_booking)
    ],
    # Optional: Enable file logging for entire namespaces
    "namespaces": [
        # "air",      # Uncomment to enable all air/* endpoints
        # "traveler", # Uncomment to enable all traveler/* endpoints
        # "users",    # Uncomment to enable all users/* endpoints
    ],
    # Global override - set to True to enable for all endpoints
    "log_all_endpoints": False,
}


def _safe_int_conversion(value: Any) -> int:
    """
    Safely convert value to integer, matching original acall_spotnana_api behavior.

    Args:
        value: Value to convert to integer

    Returns:
        Integer value or 0 if conversion fails
    """
    if isinstance(value, int):
        return value

    if isinstance(value, str):
        if value.isdigit():
            return int(value)
        # Handle case where value might be "unknown" or other non-numeric string
        return 0

    return 0


class SpotnanaLoggingConfig:
    """Configuration and utilities for Spotnana API logging."""

    @staticmethod
    def parse_url_components(url: str) -> tuple[str, str]:
        """
        Parse URL to extract namespace and endpoint.

        Examples:
            /v2/air/search -> ('air', 'search')
            /v1/trip/details -> ('trip', 'details')
            /v2/user/profile/update -> ('user', 'profile_update')

        Args:
            url: The API endpoint URL

        Returns:
            Tuple of (namespace, endpoint)
        """
        # Extract path after version (v1, v2, etc.)
        match = re.search(r"/v[12]/(.+)", url)
        if not match:
            return "unknown", "unknown"

        path_parts = match.group(1).split("/")
        if len(path_parts) < 2:
            return path_parts[0] if path_parts else "unknown", "unknown"

        namespace = path_parts[0]
        endpoint = "_".join(path_parts[1:])  # Join remaining parts with underscore

        return namespace, endpoint

    @staticmethod
    def generate_log_file_path(url: str, request_type: str) -> Path:
        """
        Generate structured log file path.

        Args:
            url: The API endpoint URL
            request_type: 'request' or 'response'

        Returns:
            Path object for the log file
        """
        namespace, endpoint = SpotnanaLoggingConfig.parse_url_components(url)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid4())[:4]

        filename = f"{namespace}_{endpoint}_{request_type}_{timestamp}_{unique_id}.json"

        return Path(settings.SPOTNANA_LOG_ROOT_DIR) / namespace / endpoint / filename

    @staticmethod
    def extract_context_variables() -> dict[str, Any]:
        """
        Extract context variables from structlog.

        Returns:
            Dictionary containing trip_id, user_id, and other context
        """
        context = structlog.contextvars.get_contextvars()

        # Convert to integers for firehose logging, fallback to string for other uses
        trip_id_raw = context.get("trip_id", "unknown")
        user_id_raw = context.get("user_id", "unknown")

        return {
            "trip_id": trip_id_raw,
            "user_id": user_id_raw,
            "trip_id_int": _safe_int_conversion(trip_id_raw),
            "user_id_int": _safe_int_conversion(user_id_raw),
            "timestamp": datetime.now().isoformat(),
        }

    @staticmethod
    def extract_method_from_url(url: str | None) -> str:
        """
        Extract API method from URL for logging purposes.

        Matches the exact logic from original acall_spotnana_api:
        search_result = re.search(r"/v[12]/(.+)", url) if url else None
        method = search_result.group(1).upper() if search_result else ""

        Args:
            url: The API endpoint URL

        Returns:
            Uppercase method name or empty string if no match
        """
        if not url:
            return ""

        search_result = re.search(r"/v[12]/(.+)", url)
        if search_result:
            return search_result.group(1).upper()
        return ""

    @staticmethod
    async def write_log_file(file_path: Path, data: dict[str, Any]) -> None:
        """
        Write log data to file asynchronously.

        Args:
            file_path: Path to the log file
            data: Data to write to the file
        """
        try:
            # Create directory if it doesn't exist
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # Write data to file
            async with aiofiles.open(file_path, "w") as file:
                await file.write(json.dumps(data, indent=2))

        except Exception as e:
            # Use structlog for error logging
            logger = structlog.get_logger(__name__)
            logger.error(f"Failed to write log file: {e}", file_path=str(file_path))

    @staticmethod
    def should_log_to_file(url: str | None = None) -> bool:
        """
        Check if file logging is enabled for the given URL.

        Matches original behavior: only log files when NOT in server context
        and the endpoint is configured for file logging.

        Args:
            url: The API endpoint URL to check

        Returns:
            True if file logging should be performed
        """
        # Only log files when NOT in server context (matches original behavior)
        if not settings.is_local:
            return False

        # If no URL provided, default to not logging
        if not url:
            return False

        # Check global override first
        if FILE_LOGGING_CONFIG.get("log_all_endpoints", False):
            return True

        # Parse URL components
        namespace, endpoint = SpotnanaLoggingConfig.parse_url_components(url)
        full_endpoint = f"{namespace}/{endpoint}"

        # Check if namespace is enabled
        if namespace in FILE_LOGGING_CONFIG.get("namespaces", []):
            return True

        # Check if specific endpoint is enabled
        if full_endpoint in FILE_LOGGING_CONFIG.get("endpoints", []):
            return True

        return False

    @staticmethod
    def should_log_to_firehose() -> bool:
        """
        Check if firehose logging is enabled.

        Firehose logging is always enabled when in server context.

        Returns:
            True if firehose logging should be performed
        """
        return not settings.is_local

    @staticmethod
    def log_request_to_firehose(
        url: str, request_data: dict[str, Any] | None, query_params: dict[str, Any] | None, response: dict[str, Any]
    ) -> None:
        """
        Log request to firehose using existing flight_utils functionality.

        Args:
            url: The API endpoint URL
            request_data: Request body data (for POST/PUT requests)
            query_params: Query parameters (for GET requests)
            response: Response data
        """
        if not SpotnanaLoggingConfig.should_log_to_firehose():
            return

        context = SpotnanaLoggingConfig.extract_context_variables()
        method = SpotnanaLoggingConfig.extract_method_from_url(url)

        # Combine request_data and query_params for the params field
        # This matches the original acall_spotnana_api behavior: params=data or params
        combined_params = request_data or query_params or {}

        try:
            flight_utils.log_request_to_firehose(
                source="SPOTNANA",
                user_id=context["user_id_int"],
                trip_id=context["trip_id_int"],
                params=combined_params,
                response=response,
                method=method,
            )
        except Exception as e:
            logger = structlog.get_logger(__name__)
            logger.error(f"Failed to log to firehose: {e}", url=url, method=method)

    @staticmethod
    def check_spotnana_errors(response: dict[str, Any]) -> None:
        """
        Check for Spotnana-specific errors in response and raise appropriate exceptions.

        Matches the exact error format from original acall_spotnana_api:
        f"Spotnana Error: {errorCode} - {errorDetail}\n\nSpotnana debugIdentifier: {debugIdentifier}"

        Args:
            response: Response data from Spotnana API

        Raises:
            Exception: If errorMessages found in response
        """
        error_messages = response.get("errorMessages")
        if error_messages and len(error_messages) > 0:
            error_detail = error_messages[0].get("errorDetail", "Unknown error")
            error_code = error_messages[0].get("errorCode", "UNKNOWN")
            debug_identifier = response.get("debugIdentifier", "")

            # Match exact format from original acall_spotnana_api
            error_str = f"Spotnana Error: {error_code} - {error_detail}\n\nSpotnana debugIdentifier: {debug_identifier}"

            raise Exception(error_str)
