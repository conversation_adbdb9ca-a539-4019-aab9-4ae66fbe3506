"""
Custom exceptions for Spotnana client operations.
Source documentation: https://developer.spotnana.com/spotnana/error_handling
"""

from typing import Any


class SpotnanaException(Exception):
    """Base exception for all Spotnana client errors."""

    # Registry of status codes to exception classes
    _status_code_registry: dict[int, type["SpotnanaException"]] = {}

    def __init__(
        self,
        status_code: int | None = None,
        response_data: dict[str, Any] | None = None,
        error_messages: list[dict[str, Any]] | None = None,
        debug_identifier: str | None = None,
        error_code: str | None = None,
        default_message: str = "API error occurred",
    ):
        self.status_code = status_code
        self.response_data = response_data or {}
        self.error_messages = error_messages or []
        self.debug_identifier = debug_identifier
        self.error_code = error_code

        # Derive message from response data
        message = self._extract_error_message(self.response_data, default_message)
        super().__init__(message)

    def __init_subclass__(cls, **kwargs):
        """Register subclasses with their handled status codes."""
        super().__init_subclass__(**kwargs)

        # Register each status code that this exception class handles
        if hasattr(cls, "handled_status_codes"):
            status_codes = getattr(cls, "handled_status_codes", [])
            for status_code in status_codes:
                SpotnanaException._status_code_registry[status_code] = cls

    def _extract_error_message(self, response_data: dict, default_message: str) -> str:
        """Extract error message from response data."""
        if not response_data:
            return str(default_message)

        # Check for errorMessages array (common Spotnana format)
        error_messages = response_data.get("errorMessages", [])
        if error_messages and isinstance(error_messages, list) and len(error_messages) > 0:
            first_error = error_messages[0]
            if isinstance(first_error, dict):
                return first_error.get("message", str(first_error))
            else:
                return str(first_error)

        # Check for single message field
        if "message" in response_data:
            return str(response_data["message"])

        # Check for error field
        if "error" in response_data:
            error_data = response_data["error"]
            if isinstance(error_data, dict):
                return error_data.get("message", str(error_data))
            else:
                return str(error_data)

        # Fallback to default message
        return str(default_message)

    def __str__(self) -> str:
        """Enhanced string representation with debug information."""
        base_msg = super().__str__()

        parts = [base_msg]

        if self.status_code:
            parts.append(f"Status: {self.status_code}")

        if self.error_code:
            parts.append(f"Error Code: {self.error_code}")

        if self.debug_identifier:
            parts.append(f"Debug ID: {self.debug_identifier}")

        if self.error_messages:
            error_details = []
            for error_msg in self.error_messages:
                if isinstance(error_msg, dict):
                    error_code = error_msg.get("errorCode", "")
                    message = error_msg.get("message", str(error_msg))
                    if error_code:
                        error_details.append(f"[{error_code}] {message}")
                    else:
                        error_details.append(message)
                else:
                    error_details.append(str(error_msg))

            if error_details:
                parts.append(f"Details: {'; '.join(error_details)}")

        return " | ".join(parts)

    @classmethod
    def from_response(
        cls,
        status_code: int | None = None,
        response_data: dict[str, Any] | None = None,
        default_message: str = "API error occurred",
    ) -> "SpotnanaException":
        """Create exception from API response with structured error parsing."""
        if not response_data:
            response_data = {}

        # Extract common error fields from response
        error_messages = response_data.get("errorMessages", [])
        debug_identifier = response_data.get("debugIdentifier")

        # Handle single error message format
        if "message" in response_data and not error_messages:
            error_messages = [{"message": response_data["message"]}]

        # Extract error code from first error message if available
        error_code = None
        if error_messages and isinstance(error_messages[0], dict):
            error_code = error_messages[0].get("errorCode")

        return cls(
            status_code=status_code,
            response_data=response_data,
            error_messages=error_messages,
            debug_identifier=debug_identifier,
            error_code=error_code,
            default_message=default_message,
        )

    @classmethod
    def create_from_status_code(
        cls,
        status_code: int,
        response_data: dict[str, Any] | None = None,
    ) -> "SpotnanaException":
        """
        Factory method to create appropriate exception based on HTTP status code.

        This eliminates the need for large if/elif chains in error handling.
        """
        # Look up the appropriate exception class for this status code
        exception_class = cls._status_code_registry.get(status_code, cls)

        # Create and return the appropriate exception instance
        return exception_class(status_code=status_code, response_data=response_data)


class SpotnanaAuthenticationError(SpotnanaException):
    """Raised when authentication with Spotnana API fails."""

    handled_status_codes = [401]

    def __init__(self, status_code: int | None = None, response_data: dict[str, Any] | None = None):
        super().__init__(
            status_code=status_code,
            response_data=response_data,
            error_code="AUTHENTICATION_FAILED",
            default_message="Authentication failed",
        )


class SpotnanaRateLimitError(SpotnanaException):
    """Raised when Spotnana API rate limits are exceeded."""

    handled_status_codes = [429]

    def __init__(self, status_code: int | None = None, response_data: dict[str, Any] | None = None):
        super().__init__(
            status_code=status_code,
            response_data=response_data,
            error_code="RATE_LIMIT_EXCEEDED",
            default_message="Rate limit exceeded",
        )


class SpotnanaValidationError(SpotnanaException):
    """Raised when request validation fails."""

    handled_status_codes = [400]

    def __init__(self, status_code: int | None = None, response_data: dict[str, Any] | None = None):
        super().__init__(
            status_code=status_code,
            response_data=response_data,
            error_code="VALIDATION_ERROR",
            default_message="Validation failed",
        )


class SpotnanaNotFoundError(SpotnanaException):
    """Raised when a requested resource is not found."""

    handled_status_codes = [404]

    def __init__(self, status_code: int | None = None, response_data: dict[str, Any] | None = None):
        super().__init__(
            status_code=status_code,
            response_data=response_data,
            error_code="NOT_FOUND",
            default_message="Resource not found",
        )


class SpotnanaServerError(SpotnanaException):
    """Raised when Spotnana API returns a server error."""

    handled_status_codes = [500, 501, 502, 503, 504, 505]

    def __init__(self, status_code: int | None = None, response_data: dict[str, Any] | None = None):
        super().__init__(
            status_code=status_code,
            response_data=response_data,
            error_code="SERVER_ERROR",
            default_message="Server error",
        )


class SpotnanaConflictError(SpotnanaException):
    """Raised when a request conflicts with the current state."""

    handled_status_codes = [409]

    def __init__(self, status_code: int | None = None, response_data: dict[str, Any] | None = None):
        super().__init__(
            status_code=status_code,
            response_data=response_data,
            error_code="CONFLICT",
            default_message="Request conflict",
        )


class SpotnanaForbiddenError(SpotnanaException):
    """Raised when access to a resource is forbidden."""

    handled_status_codes = [403]

    def __init__(self, status_code: int | None = None, response_data: dict[str, Any] | None = None):
        super().__init__(
            status_code=status_code,
            response_data=response_data,
            error_code="FORBIDDEN",
            default_message="Access forbidden",
        )
