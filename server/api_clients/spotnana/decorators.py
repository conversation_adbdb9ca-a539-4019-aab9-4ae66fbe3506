"""
Spotnana API request logging decorators.

This module provides decorators for automated logging of Spotnana API requests and responses
with structured file organization and firehose integration.
"""

import asyncio
from functools import wraps
from typing import Any, Callable, Coroutine

import structlog

from .logging_config import SpotnanaLoggingConfig


def spotnana_http_logger(method: str):
    """
    Decorator specifically for HTTP method logging.

    This decorator is designed to wrap HTTP methods (get, post, put, delete)
    and automatically log requests and responses.

    Args:
        method: HTTP method name (GET, POST, PUT, DELETE)

    Usage:
        @spotnana_http_logger("GET")
        async def get(self, endpoint: str, params: dict | None = None, **kwargs) -> Any:
            # HTTP implementation
            pass
    """

    def decorator(func: Callable[..., Coroutine[Any, Any, Any]]) -> Callable[..., Coroutine[Any, Any, Any]]:
        @wraps(func)
        async def wrapper(self, endpoint: str, *args, **kwargs) -> Any:
            # Construct full URL
            full_url = f"{self.base_url}/{endpoint.lstrip('/')}"

            # Extract request data and query params separately
            request_data = None
            query_params = None

            if method in ["POST", "PUT"]:
                request_data = kwargs.get("data") or (args[0] if args else None)
            elif method == "GET":
                query_params = kwargs.get("params")

            # For file logging, use the appropriate data based on method
            file_log_data = request_data if method in ["POST", "PUT"] else query_params

            # Log request if file logging is enabled
            if SpotnanaLoggingConfig.should_log_to_file(full_url):
                await _log_request_to_file(full_url, file_log_data, method)

            # Execute the original function
            try:
                result = await func(self, endpoint, *args, **kwargs)

                # Log response if file logging is enabled
                if SpotnanaLoggingConfig.should_log_to_file(full_url):
                    await _log_response_to_file(full_url, result)

                # Log to firehose with separated parameters
                if SpotnanaLoggingConfig.should_log_to_firehose():
                    SpotnanaLoggingConfig.log_request_to_firehose(full_url, request_data, query_params, result)

                # Check for Spotnana-specific errors
                if isinstance(result, dict):
                    SpotnanaLoggingConfig.check_spotnana_errors(result)

                return result

            except Exception as e:
                # Log the error
                logger = structlog.get_logger(__name__)
                logger.error(f"Spotnana {method} request failed", url=full_url, error=str(e))

                # Handle HTTPException by converting to appropriate Spotnana exception
                from fastapi.exceptions import HTTPException

                if isinstance(e, HTTPException):
                    # Extract response data from HTTPException detail
                    response_data = {}
                    if isinstance(e.detail, dict):
                        response_data = e.detail
                    elif e.detail:
                        response_data = {"message": str(e.detail)}
                    else:
                        response_data = {"message": "Unknown error"}

                    # Import here to avoid circular imports
                    from .exceptions import SpotnanaException

                    raise SpotnanaException.create_from_status_code(
                        status_code=e.status_code, response_data=response_data
                    )

                # For other exceptions, just re-raise
                raise

        return wrapper

    return decorator


async def _log_request_to_file(url: str, data: Any, method: str) -> None:
    """
    Log request data to file asynchronously.

    Args:
        url: The API endpoint URL
        data: Request data (params or body)
        method: HTTP method
    """
    try:
        file_path = SpotnanaLoggingConfig.generate_log_file_path(url, "request")
        context = SpotnanaLoggingConfig.extract_context_variables()

        log_data = {
            "url": url,
            "method": method,
            "data": data,
            "context": context,
        }

        # Use asyncio.create_task to avoid blocking
        asyncio.create_task(SpotnanaLoggingConfig.write_log_file(file_path, log_data))

    except Exception as e:
        logger = structlog.get_logger(__name__)
        logger.error(f"Failed to log request to file: {e}", url=url)


async def _log_response_to_file(url: str, response: Any) -> None:
    """
    Log response data to file asynchronously.

    Args:
        url: The API endpoint URL
        response: Response data
    """
    try:
        file_path = SpotnanaLoggingConfig.generate_log_file_path(url, "response")
        context = SpotnanaLoggingConfig.extract_context_variables()

        log_data = {
            "url": url,
            "response": response,
            "context": context,
        }

        # Use asyncio.create_task to avoid blocking
        asyncio.create_task(SpotnanaLoggingConfig.write_log_file(file_path, log_data))

    except Exception as e:
        logger = structlog.get_logger(__name__)
        logger.error(f"Failed to log response to file: {e}", url=url)
