import logging
import traceback
from typing import Any

import structlog
from baml_py import Collector

from baml_client.tracing import set_tags
from server.utils.settings import settings


class Logger:
    collector: Collector

    def __init__(self) -> None:
        self.collector = Collector("OTTO")

        def configure_logger(enable_json_logs: bool):
            print(f"Initializing logger in {'JSON' if enable_json_logs else 'console'} mode...")

            timestamper = structlog.processors.TimeStamper(fmt="%Y-%m-%d %H:%M:%S", utc=False)

            shared_processors = [
                timestamper,
                structlog.stdlib.add_log_level,
                structlog.contextvars.merge_contextvars,
                structlog.stdlib.ExtraAdder(),
                structlog.processors.CallsiteParameterAdder(
                    {
                        structlog.processors.CallsiteParameter.FUNC_NAME,
                        structlog.processors.CallsiteParameter.LINENO,
                        structlog.processors.CallsiteParameter.FILENAME,
                    },
                    additional_ignores=["server.utils.logger"],
                ),
            ]

            structlog.configure(
                processors=shared_processors + [structlog.stdlib.ProcessorFormatter.wrap_for_formatter],
                logger_factory=structlog.stdlib.LoggerFactory(),
                wrapper_class=structlog.stdlib.BoundLogger,
                cache_logger_on_first_use=True,
            )

            logs_render = (
                structlog.processors.JSONRenderer() if enable_json_logs else structlog.dev.ConsoleRenderer(colors=True)
            )

            _configure_default_logging_by_custom(shared_processors, logs_render)

        def _configure_default_logging_by_custom(shared_processors, logs_render):
            handler = logging.StreamHandler()

            # Use `ProcessorFormatter` to format all `logging` entries.
            formatter = structlog.stdlib.ProcessorFormatter(
                foreign_pre_chain=shared_processors,
                processors=[
                    structlog.stdlib.ProcessorFormatter.remove_processors_meta,
                    logs_render,
                ],
            )

            handler.setFormatter(formatter)
            root_uvicorn_logger = logging.getLogger()
            root_uvicorn_logger.addHandler(handler)
            root_uvicorn_logger.setLevel(logging.INFO)

        from server.utils.settings import settings

        enable_json_logs = True if not settings.is_local else False
        configure_logger(enable_json_logs)

        self.enable_json_logs = enable_json_logs
        self.logger = structlog.stdlib.get_logger()

    def debug(self, msg: str, mask: str | None = None):
        self.logger.debug(msg if mask is None or self.enable_json_logs else mask.format(msg))

    def info(self, msg: str, mask: str | None = None):
        self.logger.info(msg if mask is None or self.enable_json_logs else mask.format(msg))

    def warn(self, msg: str, mask: str | None = None):
        self.logger.warn(msg if mask is None or self.enable_json_logs else mask.format(msg))

    def warning(self, msg: str, mask: str | None = None):
        self.warn(msg, mask)

    def error(self, err: str | BaseException, mask: str | None = None):
        err = str(err)
        if settings.RUN_CONTEXT == "console":
            err += "\n" + traceback.format_exc()
        self.logger.error(err if mask is None or self.enable_json_logs else mask.format(err))

    def log_baml(self):
        if baml_log := self.collector.last:
            log = {
                "baml_function_name": baml_log.function_name,
                "metadata": baml_log.metadata,
                "output": baml_log.raw_llm_response,
                "usage": {
                    "input_tokens": baml_log.usage.input_tokens,
                    "output_tokens": baml_log.usage.output_tokens,
                },
            }

            if baml_call := baml_log.selected_call:
                log.update({"model": baml_call.client_name})
                if request := baml_call.http_request:
                    if not settings.is_local:
                        log.update(request.body.json())

            self.logger.info("baml_function", **log)

    @staticmethod
    def bind_log_context(**kw: Any):
        structlog.contextvars.bind_contextvars(**kw)
        set_tags(**kw)


logger = Logger()
