import logging

from server.utils.async_requests import make_get_request, make_post_request
from server.utils.settings import settings

logger = logging.getLogger(__name__)


class EmailValidationError(Exception):
    """Custom exception for email validation errors"""

    pass


async def check_email_with_disify(email: str) -> bool:
    """
    Check an email against disify.com API
    Returns True if email domain is disposable, False otherwise
    """
    try:
        url = f"https://disify.com/api/email/{email}"
        result = await make_get_request(url)

        # Check if domain is marked as disposable
        is_disposable = result.get("disposable", False)

        logger.info(f"Disify check: disposable={is_disposable}")
        return is_disposable

    except Exception as e:
        logger.warning(f"Error checking email with disify: {str(e)}")
        return False


async def is_domain_blocked(email: str) -> bool:
    """
    Check if the email domain is blocked using disify.com API
    """
    if not email or "@" not in email:
        return True  # Invalid email format

    if not settings.BLOCK_DISPOSABLE_EMAILS:
        logger.info("Skip disify as blocking disposable emails is disabled in settings")
        return False

    # Check with disify.com API
    try:
        is_disposable = await check_email_with_disify(email)
        if is_disposable:
            logger.info("Email detected as disposable by disify.com")
            return True
    except Exception as e:
        logger.warning(f"Failed to check with disify API: {str(e)}")
        # If API fails, be permissive and allow the email through
        return False

    return False


async def validate_email_with_mailercheck(email: str) -> bool:
    """
    Validate email using Mailercheck API
    Returns True if email is valid, False if invalid
    Raises EmailValidationError if API call fails
    """
    if not settings.MAILERCHECK_API_KEY:
        logger.error("MAILERCHECK_API_KEY is not configured")
        raise EmailValidationError("Email validation service is not available")

    if not settings.BLOCK_DISPOSABLE_EMAILS:
        logger.info("Skip mailercheck as blocking disposable emails is disabled in settings")
        return True

    try:
        url = "https://api.mailercheck.com/api/v1/check/single"
        headers = {"Authorization": f"Bearer {settings.MAILERCHECK_API_KEY}", "Content-Type": "application/json"}
        data = {"email": email}

        response_data = await make_post_request(url, headers=headers, data=data)

        # Mailercheck returns status field with different values
        status = response_data.get("status", "")
        # Only check for "Do not send to" statuses - these are truly invalid
        # Ignore "error" status as it doesn't mean the email is invalid
        # Be permissive for all other statuses including "valid" and "risky" categories
        invalid_statuses = ["syntax_error", "typo", "mailbox_not_found", "disposable", "blocked"]

        if status in invalid_statuses:
            return False
        else:
            # Be permissive for all other statuses (valid, risky, error, unknown)
            return True

    except Exception as e:
        logger.error(f"Unexpected error during email validation: {str(e)}")
        raise EmailValidationError("Email validation service error")
