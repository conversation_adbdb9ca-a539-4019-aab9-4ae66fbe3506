import json
import uuid
from datetime import datetime, timedelta, timezone
from typing import Any, Literal, cast
from zoneinfo import ZoneInfo

import dateutil.parser
from langchain_core.messages import AIMessage
from pydantic import BaseModel

from agent.agent import Agent
from baml_client import b
from baml_client.types import FlightChangeEvent, HotelChangeEvent
from flight_agent.flight_data import FlightInfo
from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.schemas.authenticate.user import User
from server.schemas.partners.spotnana.travel_delivery import SpotnanaTravelDelivery
from server.services.memory.trips.retriever import TripMemoryRetriever
from server.services.notifications.push_app_notifications import send_event_notification
from server.utils.logger import logger
from server.utils.settings import settings
from server.utils.websocket_no_op import partial_no_op
from virtual_travel_agent.helpers import get_current_date_string
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory
from virtual_travel_agent.timings import Timings

# GDS (Global Distribution System) status code mappings to normalized flight status
GDS_status_code_map = {
    # Confirmed/OK statuses
    "HK": "on_time",  # Hold Confirmed
    "OK": "on_time",  # Confirmed
    "RR": "on_time",  # Reconfirm Required (still confirmed)
    "SS": "on_time",  # Seat Requested/Confirmed
    # Boarding/Departure statuses
    "OP": "boarding",  # Flight Open for Check-in
    "BD": "boarding",  # Boarding
    "DP": "departed",  # Departed
    "CL": "departed",  # Flight Closed
    # Delay statuses
    "DL": "delayed",  # Delayed
    "RT": "delayed",  # Retimed/Delayed
    # Cancellation statuses
    "XX": "cancelled",  # Cancelled
    "XL": "cancelled",  # Cancelled
    "CN": "cancelled",  # Cancelled
    "NO": "cancelled",  # Unconfirmed
    # Waitlist/Request statuses (treat as on_time since confirmed)
    "WL": "on_time",  # Waitlist (if in booking, it's confirmed)
    "RQ": "on_time",  # Request (if in booking, it's confirmed)
    "HL": "on_time",  # Holding List
    # Special statuses
    "UC": "on_time",  # Unable to Confirm (but if in booking, treat as confirmed)
    "UN": "on_time",  # Unable (but if in booking, treat as confirmed)
    "TK": "on_time",  # Ticketed
    "TL": "on_time",  # Ticketed
}

# Spotnana leg status mappings to normalized flight status
SPOTNANA_leg_status_map = {
    "CONFIRMED_STATUS": "on_time",
    "CANCELLED_STATUS": "cancelled",
    "UNCONFIRMED_STATUS": "delayed",
    "AIRLINE_CONTROL_STATUS": "on time",
    "COMPLETED_STATUS": "departed",
    "ACTIVE_STATUS": "on_time",
    "PENDING_STATUS": "pending",
    "PROCESSING_STATUS": "pending",
    "PAYMENT_DECLINED_STATUS": "payment declined",
    "FLIGHT_UNCONFIRMED_STATUS": "pending",
    "UNKNOWN_STATUS": "unknown",
    "REFUNDED_STATUS": "cancelled",
    "VOIDED_STATUS": "cancelled",
    "CANCELLATION_IN_PROGRESS_STATUS": "cancelling",
    "INOPERATIVE_STATUS": "cancelled",
}


class FieldChange(BaseModel):
    field_name: str  # "departure_time", "arrival_time", "gate", etc.
    old_value: str | None  # Previous value
    new_value: str | None  # New value


class FlightUpdate(BaseModel):
    update_id: str  # Unique identifier for this update
    timestamp: datetime  # When the update occurred
    severity: Literal["critical", "moderate", "minor"]
    change_type: Literal["schedule", "gate", "terminal", "boarding", "seat", "cancellation"]
    changes: list[FieldChange]  # Multiple field changes in one update
    change_summary: str  # Human-readable summary
    source: str  # "spotnana_webhook"


class HotelUpdate(BaseModel):
    update_id: str
    timestamp: datetime
    severity: Literal["critical", "moderate", "minor"]
    change_type: Literal["check_in", "check_out", "cancellation"]
    changes: list[FieldChange]  # Multiple field changes in one update
    change_summary: str  # Human-readable summary
    source: str  # "booking_com_api"


class InTripFlightInfo(FlightInfo):
    leg_index: int
    confirmation_number: str
    boarding_time: str | None = None
    gate: str | None = None
    terminal: str | None = None
    current_status: str | None = None  # "on_time", "delayed", "cancelled", etc.

    # Leg-level update tracking
    updates: list[FlightUpdate] = []
    last_updated_at: datetime | None = None

    def get_departure_datetime(self) -> datetime | None:
        """Parse and return departure datetime from departure time and timezone."""
        if not self.departure_time:
            return None

        try:
            departure_tz = ZoneInfo(self.departure_timezone) if self.departure_timezone else timezone.utc
            departure_dt = dateutil.parser.isoparse(self.departure_time).astimezone(departure_tz)
            return departure_dt
        except Exception as e:
            logger.warning(f"Failed to parse departure datetime: {self.departure_time}, error: {e}")
            return None

    def get_arrival_datetime(self) -> datetime | None:
        """Parse and return arrival datetime from arrival time and timezone."""
        if not self.arrival_time:
            return None

        try:
            arrival_tz = ZoneInfo(self.arrival_timezone) if self.arrival_timezone else timezone.utc
            arrival_dt = dateutil.parser.isoparse(self.arrival_time).astimezone(arrival_tz)
            return arrival_dt
        except Exception as e:
            logger.warning(f"Failed to parse arrival datetime: {self.arrival_time}, error: {e}")
            return None

    # NEW: Leg-level update methods
    def add_update(self, update: FlightUpdate) -> None:
        """Add a new update to this specific leg"""
        self.updates.append(update)
        self.last_updated_at = datetime.now(timezone.utc)

    def get_recent_updates(self, hours: int = 24) -> list[FlightUpdate]:
        """Get updates within specified hours for this leg"""
        if not self.updates:
            return []

        cutoff = datetime.now(timezone.utc) - timedelta(hours=hours)
        return [u for u in self.updates if u.timestamp > cutoff]

    def get_change_summary(self) -> str | None:
        """Get human-readable summary of recent changes for this leg"""
        recent = self.get_recent_updates(2)  # Last 2 hours
        if not recent:
            return None
        return "; ".join(u.change_summary for u in recent)

    def has_critical_changes(self) -> bool:
        """Check if this leg has any critical changes"""
        return any(u.severity == "critical" for u in self.updates)


class InTripFlightBooking(BaseModel):
    legs: list[InTripFlightInfo]
    origin_name: str = ""
    origin_code: str = ""
    departure_time: str = ""
    departure_timezone: str = ""
    departure_datetime: datetime | None = None
    arrival_time: str = ""
    arrival_timezone: str = ""
    arrival_datetime: datetime | None = None
    destination_name: str = ""
    destination_code: str = ""

    # Updates tracking fields
    updates: list[FlightUpdate] = []
    last_updated_at: datetime | None = None

    def __init__(self, **data):
        super().__init__(**data)
        self._populate_flight_details()

    def _populate_flight_details(self) -> None:
        """Populate flight-level fields from legs data.

        Departure fields come from first leg's departure info.
        Arrival fields come from last leg's arrival info.
        """
        if not self.legs:
            return

        # Get departure info from first leg
        first_leg = self.legs[0]
        self.origin_name = first_leg.origin_name
        self.origin_code = first_leg.origin_code
        self.departure_time = first_leg.departure_time
        self.departure_timezone = first_leg.departure_timezone
        self.departure_datetime = self.get_departure_datetime()

        # Get arrival info from last leg
        last_leg = self.legs[-1]
        self.destination_name = last_leg.destination_name
        self.destination_code = last_leg.destination_code
        self.arrival_time = last_leg.arrival_time
        self.arrival_timezone = last_leg.arrival_timezone
        self.arrival_datetime = self.get_arrival_datetime()

    def get_departure_datetime(self) -> datetime | None:
        """Parse and return departure datetime from departure time and timezone."""
        if not self.departure_time:
            return None

        try:
            departure_tz = ZoneInfo(self.departure_timezone) if self.departure_timezone else timezone.utc
            departure_dt = dateutil.parser.isoparse(self.departure_time).astimezone(departure_tz)
            return departure_dt
        except Exception as e:
            logger.warning(f"Failed to parse departure datetime: {self.departure_time}, error: {e}")
            return None

    def get_arrival_datetime(self) -> datetime | None:
        """Parse and return arrival datetime from arrival time and timezone."""
        if not self.arrival_time:
            return None

        try:
            arrival_tz = ZoneInfo(self.arrival_timezone) if self.arrival_timezone else timezone.utc
            arrival_dt = dateutil.parser.isoparse(self.arrival_time).astimezone(arrival_tz)
            return arrival_dt
        except Exception as e:
            logger.warning(f"Failed to parse arrival datetime: {self.arrival_time}, error: {e}")
            return None

    # NEW: Helper methods for update tracking
    def get_effective_status(self) -> str:
        """Get current status with smart defaults from first leg"""
        if self.legs and self.legs[0].current_status:
            return self.legs[0].current_status
        return self._determine_status_from_times()

    def _determine_status_from_times(self) -> str:
        """Determine status based on departure time comparison"""
        try:
            if not self.departure_time:
                return "scheduled"

            departure_dt = dateutil.parser.isoparse(self.departure_time)
            if self.departure_timezone:
                departure_tz = ZoneInfo(self.departure_timezone)
                departure_dt = departure_dt.replace(tzinfo=departure_tz)

            current_time = datetime.now(timezone.utc)
            departure_minutes_left = (departure_dt - current_time).total_seconds() / 60

            if departure_minutes_left > 30:
                return "on_time"
            elif departure_minutes_left > 0:
                return "boarding_soon"
            else:
                return "departed"
        except Exception as e:
            logger.warning(f"Failed to determine status from times: {e}")
            return "scheduled"

    def _normalize_flight_status(self, raw_status: str | None, leg_info: InTripFlightInfo | None = None) -> str:
        """
        Normalize raw flight status codes to human-readable status values.

        Args:
            raw_status: Raw status code from webhook (e.g. "HK", "UC", "NO")
            leg_info: Flight leg info for fallback time-based determination

        Returns:
            Normalized status string like "on_time", "delayed", "cancelled", etc.
        """
        if not raw_status:
            # No status provided, use time-based logic if leg info is available
            if leg_info:
                return self._determine_status_from_leg_times(leg_info)
            return "scheduled"

        # Convert to uppercase for consistent matching
        status_upper = raw_status.upper().strip()

        # Check for exact match in GDS status code mapping first
        if status_upper in GDS_status_code_map:
            return GDS_status_code_map[status_upper]

        # Check for partial matches for complex status codes
        status_lower = raw_status.lower()

        # Handle status codes with keywords
        if any(keyword in status_lower for keyword in ["cancel", "cancelled"]):
            return "cancelled"
        elif any(keyword in status_lower for keyword in ["delay", "delayed"]):
            return "delayed"
        elif any(keyword in status_lower for keyword in ["board", "boarding"]):
            return "boarding"
        elif any(keyword in status_lower for keyword in ["depart", "departed"]):
            return "departed"
        elif any(keyword in status_lower for keyword in ["confirm", "confirmed", "ok"]):
            return "on_time"

        # For unknown status codes, fall back to time-based determination
        logger.info(f"Unknown flight status code '{raw_status}', falling back to time-based determination")
        if leg_info:
            return self._determine_status_from_leg_times(leg_info)

        # Final fallback
        return "on_time"

    def _determine_status_from_leg_times(self, leg_info: InTripFlightInfo) -> str:
        """Determine status based on leg departure time comparison"""
        try:
            if not leg_info.departure_time:
                return "scheduled"

            departure_dt = dateutil.parser.isoparse(leg_info.departure_time)
            if leg_info.departure_timezone:
                departure_tz = ZoneInfo(leg_info.departure_timezone)
                departure_dt = departure_dt.replace(tzinfo=departure_tz)

            current_time = datetime.now(timezone.utc)
            departure_minutes_left = (departure_dt - current_time).total_seconds() / 60

            if departure_minutes_left > 30:
                return "on_time"
            elif departure_minutes_left > 0:
                return "boarding_soon"
            else:
                return "departed"
        except Exception as e:
            logger.warning(f"Failed to determine status from leg times: {e}")
            return "scheduled"

    def get_recent_updates(self, hours: int = 24) -> list[FlightUpdate]:
        """Get updates within specified hours"""
        if not self.updates:
            return []

        cutoff = datetime.now(timezone.utc) - timedelta(hours=hours)
        return [u for u in self.updates if u.timestamp > cutoff]

    def get_change_summary(self) -> str | None:
        """Get human-readable summary of recent changes"""
        recent = self.get_recent_updates(2)  # Last 2 hours
        if not recent:
            return None
        return "; ".join(u.change_summary for u in recent)

    def add_update(self, update: FlightUpdate) -> None:
        """Add a new update to the flight booking"""
        self.updates.append(update)
        self.last_updated_at = datetime.now(timezone.utc)

    def get_effective_gate(self) -> str | None:
        """Get gate from legs (first leg with gate info)"""
        if self.legs and self.legs[0].gate:
            return self.legs[0].gate
        return None

    def get_effective_terminal(self) -> str | None:
        """Get terminal from legs (first leg with terminal info)"""
        if self.legs and self.legs[0].terminal:
            return self.legs[0].terminal
        return None

    def get_effective_boarding_time(self) -> str | None:
        """Get boarding time from legs (first leg with boarding time info)"""
        if self.legs and self.legs[0].boarding_time:
            return self.legs[0].boarding_time
        return None

    # NEW: Dual-level update aggregation methods
    def get_all_updates(self) -> list[tuple[int | None, FlightUpdate]]:
        """Get all updates: (None, trip_update) + (leg_idx, leg_update)"""
        all_updates = []

        # Add trip-wide updates
        for update in self.updates:
            all_updates.append((None, update))

        # Add leg-specific updates
        for leg in self.legs:
            for update in leg.updates:
                all_updates.append((leg.leg_index, update))

        # Sort by timestamp
        return sorted(all_updates, key=lambda x: x[1].timestamp)

    def get_recent_updates_combined(self, hours: int = 24) -> list[FlightUpdate]:
        """Get recent updates from trip + all legs combined"""
        all_updates = []

        # Get trip-wide recent updates
        all_updates.extend(self.get_recent_updates(hours))

        # Get leg-specific recent updates
        for leg in self.legs:
            all_updates.extend(leg.get_recent_updates(hours))

        # Sort by timestamp and return
        return sorted(all_updates, key=lambda x: x.timestamp)

    def add_update_to_leg(self, leg_index: int, update: FlightUpdate) -> None:
        """Add update to specific leg"""
        for leg in self.legs:
            if leg.leg_index == leg_index:
                leg.add_update(update)
                return
        raise ValueError(f"Leg index {leg_index} not found")

    def get_leg_updates(self, leg_index: int) -> list[FlightUpdate]:
        """Get updates for specific leg"""
        for leg in self.legs:
            if leg.leg_index == leg_index:
                return leg.updates
        raise ValueError(f"Leg index {leg_index} not found")

    def get_trip_update_summary(self) -> str | None:
        """Get human-readable summary of trip-wide changes only"""
        recent = self.get_recent_updates(2)  # Last 2 hours, trip-wide only
        if not recent:
            return None
        return "; ".join(u.change_summary for u in recent)

    def has_any_critical_changes(self) -> bool:
        """Check if there are any critical changes at trip or leg level"""
        # Check trip-wide critical changes
        if any(u.severity == "critical" for u in self.updates):
            return True

        # Check leg-level critical changes
        return any(leg.has_critical_changes() for leg in self.legs)

    # Backward compatibility helpers
    def migrate_booking_updates_to_legs(self) -> None:
        """Migrate old booking-level updates to appropriate legs based on field changes"""
        if not self.updates:
            return

        # For now, assign booking-level updates to first leg for backward compatibility
        # In the future, this could be made smarter by analyzing field changes
        if self.legs:
            first_leg = self.legs[0]
            first_leg.updates.extend(self.updates)
            if self.last_updated_at and (
                not first_leg.last_updated_at or self.last_updated_at > first_leg.last_updated_at
            ):
                first_leg.last_updated_at = self.last_updated_at

    def is_legacy_format(self) -> bool:
        """Check if this booking uses legacy booking-level updates only"""
        has_booking_updates = len(self.updates) > 0
        has_leg_updates = any(len(leg.updates) > 0 for leg in self.legs)
        return has_booking_updates and not has_leg_updates


class InTripHotelBooking(BaseModel):
    id: int
    hotel_name: str
    check_in_date: str
    check_in_time: str
    check_out_date: str
    check_out_time: str
    room_type: str  # This gets updated directly from webhooks
    address: str
    confirmation_number: str
    status: str | None = None  # This gets updated directly from webhooks

    # NEW: Update tracking fields
    updates: list[HotelUpdate] = []
    last_updated_at: datetime | None = None

    def get_check_in_datetime(self) -> datetime | None:
        """Parse and return check-in datetime from date and time strings."""
        if not self.check_in_date:
            return None

        try:
            if self.check_in_time:
                return dateutil.parser.isoparse(f"{self.check_in_date}T{self.check_in_time}")
            else:
                return dateutil.parser.isoparse(self.check_in_date)
        except Exception as e:
            datetime_str = f"{self.check_in_date}T{self.check_in_time}" if self.check_in_time else self.check_in_date
            logger.warning(f"Failed to parse check-in datetime: {datetime_str}, error: {e}")
            return None

    def get_check_out_datetime(self) -> datetime | None:
        """Parse and return check-out datetime from date and time strings."""
        if not self.check_out_date:
            return None

        try:
            if self.check_out_time:
                return dateutil.parser.isoparse(f"{self.check_out_date}T{self.check_out_time}")
            else:
                return dateutil.parser.isoparse(self.check_out_date)
        except Exception as e:
            datetime_str = (
                f"{self.check_out_date}T{self.check_out_time}" if self.check_out_time else self.check_out_date
            )
            logger.warning(f"Failed to parse check-out datetime: {datetime_str}, error: {e}")
            return None

    # NEW: Helper methods for update tracking
    def get_recent_updates(self, hours: int = 24) -> list[HotelUpdate]:
        """Get updates within specified hours"""
        if not self.updates:
            return []

        cutoff = datetime.now(timezone.utc) - timedelta(hours=hours)
        return [u for u in self.updates if u.timestamp > cutoff]

    def has_critical_changes(self) -> bool:
        """Check if there are any critical changes"""
        return any(u.severity == "critical" for u in self.updates)

    def get_change_summary(self) -> str | None:
        """Get human-readable summary of recent changes"""
        recent = self.get_recent_updates(2)  # Last 2 hours
        if not recent:
            return None
        return "; ".join(u.change_summary for u in recent)

    def add_update(self, update: HotelUpdate) -> None:
        """Add a new update to the hotel booking"""
        self.updates.append(update)
        self.last_updated_at = datetime.now(timezone.utc)


class InTripAgent(Agent):
    def __init__(self, user: User, thread: ChatThread):
        self.user = user
        self.thread = thread
        super().__init__(
            user=user,
            history=PostgresChatMessageHistory(thread_id=thread.id),
            mem_loader=TripMemoryRetriever(user_id=str(user.id), thread_id=str(thread.id)),
            websocket_send_message=partial_no_op,
        )

    async def handle_flight_changes_for_in_trip(
        self, data: SpotnanaTravelDelivery, booking: Booking, thread: ChatThread, user: User
    ):
        payload: dict = data.payload

        try:
            is_booking_in_trip = await InTripAgent.is_in_trip_booking(booking)
            if not is_booking_in_trip:
                logger.info(f"[IN_TRIP] Flight booking {booking.id} is not in trip, skipping.")
                return

            extracted_data: dict[str, Any] = await InTripAgent.extract_event_details(payload)

            t = Timings("BAML: ProcessFlightChangeEvent")
            results: FlightChangeEvent = await b.ProcessFlightChangeEvent(
                existing_booking=json.dumps(booking.content),
                change_event_details=json.dumps(extracted_data),
                current_date=get_current_date_string(),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("green")
            logger.log_baml()

            # NEW: Process and store flight updates
            await self._process_flight_webhook_updates(booking, extracted_data, results)

            logger.info(f"[IN_TRIP] Sending notification to user: {user.email}.")

            notificatioin_event = {
                "summary": results.change_summary,
                "description": results.agent_response,
            }
            await send_event_notification(notificatioin_event, user.email)

            msg = AIMessage(content=results.agent_response)
            msg.additional_kwargs = {
                "agent_classification": "InTrip",
                "is_in_trip_openning": True,
            }
            await self.persist_messages([msg])

        except Exception as e:
            logger.error(f"[IN_TRIP] Error handling flight changes: {str(e)}")

    async def handle_hotel_change_for_in_trip(self, hotel_booking: Booking, change: dict[str, Any], user: User):
        """Handle hotel changes from booking.com for in-trip bookings."""
        try:
            status = change.get("status", "")
            is_booking_in_trip = await InTripAgent.is_in_trip_booking(hotel_booking)
            if not is_booking_in_trip:
                logger.info(f"[IN_TRIP] Hotel booking {hotel_booking.id} is not in trip, skipping.")
                return

            t = Timings("BAML: ProcessHotelChangeEvent")
            results: HotelChangeEvent = await b.ProcessHotelChangeEvent(
                existing_booking=json.dumps(hotel_booking.content),
                change_details=json.dumps(status),
                current_date=get_current_date_string(),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("green")
            logger.log_baml()

            # NEW: Process and store hotel updates
            await self._process_hotel_webhook_updates(hotel_booking, change, results)

            logger.info(f"[IN_TRIP] Sending hotel change notification to user: {user.email}.")

            notification_event = {
                "summary": results.change_summary,
                "description": results.agent_response,
            }
            await send_event_notification(notification_event, user.email)

            msg = AIMessage(content=results.agent_response)
            msg.additional_kwargs = {
                "agent_classification": "InTrip",
                "is_in_trip_openning": True,
                "change_type": "hotel",
            }
            await self.persist_messages([msg])

        except Exception as e:
            logger.error(f"[IN_TRIP] Error handling hotel change: {str(e)}")

    def _compare_and_update_flight_legs(
        self, flight_booking: InTripFlightBooking, webhook_legs: list[dict]
    ) -> dict[int, list[FieldChange]]:
        """Compare webhook flight data against current flight legs and update them.

        Returns:
            dict[int, list[FieldChange]]: Changes grouped by leg_index
        """
        changes_by_leg = {}

        try:
            # Process each webhook leg
            for leg_idx, webhook_leg in enumerate(webhook_legs):
                webhook_flights = webhook_leg.get("flights", [])

                # Match against existing flight legs
                for flight_idx, webhook_flight in enumerate(webhook_flights):
                    # Find corresponding leg in flight_booking
                    matching_leg = None
                    for leg in flight_booking.legs:
                        if leg.leg_index == leg_idx:
                            matching_leg = leg
                            break

                    if not matching_leg:
                        continue

                    # Initialize changes list for this leg if not exists
                    if leg_idx not in changes_by_leg:
                        changes_by_leg[leg_idx] = []

                    # Compare and update departure time
                    webhook_departure = webhook_flight.get("departureDateTime", {}).get("iso8601")
                    if webhook_departure and webhook_departure != matching_leg.departure_time:
                        changes_by_leg[leg_idx].append(
                            FieldChange(
                                field_name="departure_time",
                                old_value=matching_leg.departure_time,
                                new_value=webhook_departure,
                            )
                        )
                        matching_leg.departure_time = webhook_departure

                    # Compare and update arrival time
                    webhook_arrival = webhook_flight.get("arrivalDateTime", {}).get("iso8601")
                    if webhook_arrival and webhook_arrival != matching_leg.arrival_time:
                        changes_by_leg[leg_idx].append(
                            FieldChange(
                                field_name="arrival_time",
                                old_value=matching_leg.arrival_time,
                                new_value=webhook_arrival,
                            )
                        )
                        matching_leg.arrival_time = webhook_arrival

                    # Compare and update gate
                    webhook_gate = webhook_flight.get("departureGate", {}).get("gate")
                    if webhook_gate and webhook_gate != matching_leg.gate:
                        changes_by_leg[leg_idx].append(
                            FieldChange(
                                field_name="gate",
                                old_value=matching_leg.gate,
                                new_value=webhook_gate,
                            )
                        )
                        matching_leg.gate = webhook_gate

                    # Compare and update terminal
                    webhook_terminal = webhook_flight.get("departureGate", {}).get("terminal")
                    if webhook_terminal and webhook_terminal != matching_leg.terminal:
                        changes_by_leg[leg_idx].append(
                            FieldChange(
                                field_name="terminal",
                                old_value=matching_leg.terminal,
                                new_value=webhook_terminal,
                            )
                        )
                        matching_leg.terminal = webhook_terminal

                    # Compare and update flight status
                    webhook_raw_status = webhook_flight.get("sourceStatus")
                    webhook_status = flight_booking._normalize_flight_status(webhook_raw_status, matching_leg)
                    if webhook_status and webhook_status != matching_leg.current_status:
                        changes_by_leg[leg_idx].append(
                            FieldChange(
                                field_name="flight_status",
                                old_value=matching_leg.current_status,
                                new_value=webhook_status,
                            )
                        )
                        matching_leg.current_status = webhook_status

            # Re-populate flight-level aggregate fields after leg updates
            flight_booking._populate_flight_details()

        except Exception as e:
            logger.error(f"[IN_TRIP] Error comparing and updating flight legs: {str(e)}")

        return changes_by_leg

    async def _process_flight_webhook_updates(
        self, booking: Booking, extracted_data: dict[str, Any], results: FlightChangeEvent
    ):
        """Process flight webhook data and create updates in booking content"""
        try:
            # Extract current flight booking from booking.content
            flight_booking = InTripAgent._extract_flight_details(booking)
            if not flight_booking:
                logger.error(f"[IN_TRIP] Could not extract flight details from booking {booking.id}")
                return

            # Process webhook legs data and update flight legs
            webhook_legs = extracted_data.get("legs", [])
            changes_by_leg = {}
            if webhook_legs:
                changes_by_leg = self._compare_and_update_flight_legs(flight_booking, webhook_legs)

            # Process additional flight status changes from flightSourceStatuses
            flight_statuses = extracted_data.get("flightSourceStatuses", [])
            for status_info in flight_statuses:
                raw_source_status = status_info.get("sourceStatus", "")
                # Normalize the status before using it
                normalized_status = flight_booking._normalize_flight_status(
                    raw_source_status, flight_booking.legs[0] if flight_booking.legs else None
                )
                # Update status on first leg (primary leg) if not already updated
                if flight_booking.legs and normalized_status != flight_booking.legs[0].current_status:
                    # Add to leg 0's changes if not already tracked
                    leg_0_changes = changes_by_leg.get(0, [])
                    status_already_tracked = any(
                        change.field_name == "flight_status" and change.new_value == normalized_status
                        for change in leg_0_changes
                    )
                    if not status_already_tracked:
                        if 0 not in changes_by_leg:
                            changes_by_leg[0] = []
                        changes_by_leg[0].append(
                            FieldChange(
                                field_name="flight_status",
                                old_value=flight_booking.legs[0].current_status,
                                new_value=normalized_status,
                            )
                        )
                        flight_booking.legs[0].current_status = normalized_status

            # Create updates for each affected leg
            total_changes = 0
            if changes_by_leg:
                # Use BAML-generated results for base information
                baml_severity = results.change_Severity.lower()  # Use BAML severity
                change_summary = results.change_summary  # Use BAML summary

                # Validate severity to match type constraints
                if baml_severity in ["critical", "moderate", "minor"]:
                    severity = cast(Literal["critical", "moderate", "minor"], baml_severity)
                else:
                    severity = cast(Literal["critical", "moderate", "minor"], "minor")  # default fallback

                # Create separate updates for each affected leg
                for leg_index, field_changes in changes_by_leg.items():
                    if field_changes:  # Only process legs with actual changes
                        change_type = self._determine_change_type_from_changes(field_changes)

                        # Create leg-specific summary
                        leg_summary = f"Leg {leg_index}: {change_summary}"

                        update = FlightUpdate(
                            update_id=str(uuid.uuid4()),
                            timestamp=datetime.now(timezone.utc),
                            severity=severity,
                            change_type=change_type,
                            changes=field_changes,
                            change_summary=leg_summary,
                            source="spotnana_webhook",
                        )

                        # Add update to the specific leg
                        flight_booking.add_update_to_leg(leg_index, update)
                        total_changes += len(field_changes)

                # Update booking content in database using smart delta update
                all_changes = [change for changes in changes_by_leg.values() for change in changes]
                await self._update_booking_content_with_delta(booking, flight_booking, all_changes)
                logger.info(
                    f"[IN_TRIP] Updated flight booking {booking.id} with {total_changes} field changes across {len(changes_by_leg)} legs"
                )

        except Exception as e:
            logger.error(f"[IN_TRIP] Error processing flight webhook updates: {str(e)}")

    async def _update_booking_content_with_delta(
        self, booking: Booking, flight_booking: InTripFlightBooking, field_changes: list[FieldChange]
    ) -> None:
        """
        Smart delta update that preserves existing database structure while updating changed fields.

        This method maps InTripFlightBooking changes back to the database content structure,
        preserving existing fields like 'cancelled' and adding new 'current_status' field.
        """
        try:
            content = booking.content.copy()  # Work with a copy to avoid modifying original

            # Build update dictionary for database
            updates = {}

            # Process each leg in the flight_booking to update corresponding database paths
            for leg in flight_booking.legs:
                leg_idx = leg.leg_index

                # Add current_status field (new field we're adding)
                if leg.current_status:
                    updates[f"content.legs.{leg_idx}.current_status"] = leg.current_status

                # Map updated fields to database structure
                # Database structure: content.legs[i].flight_segments[0].flight_stops[0].{field}

                if hasattr(leg, "departure_time") and leg.departure_time:
                    updates[f"content.legs.{leg_idx}.flight_segments.0.flight_stops.0.departure"] = leg.departure_time

                if hasattr(leg, "arrival_time") and leg.arrival_time:
                    # For arrival time, use the last flight stop (could be same as first for direct flights)
                    updates[f"content.legs.{leg_idx}.flight_segments.0.flight_stops.-1.arrival"] = leg.arrival_time

                if hasattr(leg, "gate") and leg.gate:
                    updates[f"content.legs.{leg_idx}.flight_segments.0.flight_stops.0.gate"] = leg.gate

                if hasattr(leg, "terminal") and leg.terminal:
                    updates[f"content.legs.{leg_idx}.flight_segments.0.flight_stops.0.terminal"] = leg.terminal

                if hasattr(leg, "seat") and leg.seat:
                    updates[f"content.legs.{leg_idx}.flight_segments.0.flight_stops.0.seat"] = leg.seat

                # Also update outbound/return if they exist (for backward compatibility)
                if leg_idx == 0 and "outbound" in content:
                    if hasattr(leg, "departure_time") and leg.departure_time:
                        updates["content.outbound.flight_segments.0.flight_stops.0.departure_time"] = leg.departure_time
                    if hasattr(leg, "arrival_time") and leg.arrival_time:
                        updates["content.outbound.flight_segments.0.flight_stops.-1.arrival_time"] = leg.arrival_time

                elif leg_idx == 1 and "return" in content and content["return"]:
                    if hasattr(leg, "departure_time") and leg.departure_time:
                        updates["content.return.flight_segments.0.flight_stops.0.departure_time"] = leg.departure_time
                    if hasattr(leg, "arrival_time") and leg.arrival_time:
                        updates["content.return.flight_segments.0.flight_stops.-1.arrival_time"] = leg.arrival_time

            # Apply the updates to the database
            if updates:
                await Booking.update_fields({"id": booking.id}, updates)
                logger.info(f"[IN_TRIP] Applied {len(updates)} delta updates to booking {booking.id}")
            else:
                logger.info(f"[IN_TRIP] No delta updates needed for booking {booking.id}")

        except Exception as e:
            logger.error(f"[IN_TRIP] Error applying delta updates to booking {booking.id}: {str(e)}")
            # Fallback to full content replacement if delta update fails
            await Booking.update_fields({"id": booking.id}, {"content": flight_booking.model_dump()})
            logger.info(f"[IN_TRIP] Used fallback full content replacement for booking {booking.id}")

    def _determine_flight_severity_from_changes(self, field_changes: list[FieldChange]) -> str:
        """Determine severity based on field changes"""
        # Check if any status changes are critical
        for change in field_changes:
            if change.field_name == "legs[0].current_status" and change.new_value:
                return self._determine_flight_severity(change.new_value)

        # Check if schedule changes are significant
        if any(change.field_name in ["departure_time", "arrival_time"] for change in field_changes):
            return "moderate"

        return "minor"

    def _determine_change_type_from_changes(
        self, field_changes: list[FieldChange]
    ) -> Literal["schedule", "gate", "terminal", "boarding", "seat", "cancellation"]:
        """Determine change type based on field changes"""
        field_names = [change.field_name for change in field_changes]

        if "flight_status" in field_names:
            return "schedule"
        elif any(field in field_names for field in ["departure_time", "arrival_time"]):
            return "schedule"
        elif any(field in field_names for field in ["gate", "terminal"]):
            return "gate"
        else:
            return "boarding"

    def _build_flight_change_summary(self, field_changes: list[FieldChange]) -> str:
        """Build human-readable summary of changes"""
        summaries = []

        for change in field_changes:
            if change.field_name == "flight_status":
                summaries.append(f"Status changed to {change.new_value}")
            elif change.field_name == "departure_time":
                old_time = self._format_time_for_summary(change.old_value)
                new_time = self._format_time_for_summary(change.new_value)
                summaries.append(f"Departure {old_time} → {new_time}")
            elif change.field_name == "arrival_time":
                old_time = self._format_time_for_summary(change.old_value)
                new_time = self._format_time_for_summary(change.new_value)
                summaries.append(f"Arrival {old_time} → {new_time}")
            elif change.field_name == "gate":
                summaries.append(f"Gate changed from {change.old_value or 'TBD'} to {change.new_value}")
            elif change.field_name == "terminal":
                summaries.append(f"Terminal changed from {change.old_value or 'TBD'} to {change.new_value}")

        return "; ".join(summaries)

    def _format_time_for_summary(self, time_str: str | None) -> str:
        """Format time string for change summary"""
        if not time_str:
            return "TBD"

        try:
            dt = dateutil.parser.isoparse(time_str)
            return dt.strftime("%I:%M %p")
        except Exception:
            return time_str

    async def _process_hotel_webhook_updates(self, booking: Booking, change: dict[str, Any], results: HotelChangeEvent):
        """Process hotel webhook data and create updates in booking content"""
        try:
            # Extract current hotel booking from booking.content
            hotel_booking = InTripAgent._extract_hotel_details(booking)
            if not hotel_booking:
                logger.error(f"[IN_TRIP] Could not extract hotel details from booking {booking.id}")
                return

            # Track all changes in this webhook event
            field_changes = []

            # Process status changes
            new_status = change.get("status", "")
            if new_status and new_status != hotel_booking.status:
                field_changes.append(
                    FieldChange(field_name="status", old_value=hotel_booking.status, new_value=new_status)
                )
                hotel_booking.status = new_status

            # Create single update if there were changes
            if field_changes:
                severity_str = self._determine_hotel_severity(new_status)
                change_summary = f"Hotel status changed to {new_status}"

                # Validate severity to match type constraints
                if severity_str in ["critical", "moderate", "minor"]:
                    severity = cast(Literal["critical", "moderate", "minor"], severity_str)
                else:
                    severity = cast(Literal["critical", "moderate", "minor"], "minor")  # default fallback

                update = HotelUpdate(
                    update_id=str(uuid.uuid4()),
                    timestamp=datetime.now(timezone.utc),
                    severity=severity,
                    change_type="cancellation",
                    changes=field_changes,
                    change_summary=change_summary,
                    source="booking_com_api",
                )

                hotel_booking.add_update(update)

                # Update booking content in database
                await Booking.update_fields({"id": booking.id}, {"content": hotel_booking.model_dump()})
                logger.info(f"[IN_TRIP] Updated hotel booking {booking.id} with {len(field_changes)} field changes")

        except Exception as e:
            logger.error(f"[IN_TRIP] Error processing hotel webhook updates: {str(e)}")

    def _determine_flight_severity(self, status: str) -> str:
        """Determine severity of flight status change"""
        critical_statuses = ["cancelled", "diverted", "emergency"]
        moderate_statuses = ["delayed", "boarding", "departed_late"]

        status_lower = status.lower()

        if any(crit in status_lower for crit in critical_statuses):
            return "critical"
        elif any(mod in status_lower for mod in moderate_statuses):
            return "moderate"
        else:
            return "minor"

    def _determine_hotel_severity(self, status: str) -> str:
        """Determine severity of hotel status change"""
        critical_statuses = ["cancelled", "cancelled_by_accommodation", "cancelled_by_guest"]
        moderate_statuses = ["no_show", "modified"]

        status_lower = status.lower()

        if any(crit in status_lower for crit in critical_statuses):
            return "critical"
        elif any(mod in status_lower for mod in moderate_statuses):
            return "moderate"
        else:
            return "minor"

    @staticmethod
    async def extract_event_details(resource):
        try:
            extracted_data = {}

            source_info = resource.get("sourceInfo", {})
            extracted_data["sourcePnrId"] = source_info.get("sourcePnrId")

            air_pnr = resource.get("airPnr", {})
            extracted_data["legs"] = air_pnr.get("legs", [])
            extracted_data["disruptedFlightDetails"] = air_pnr.get("disruptedFlightDetails", [])
            extracted_data["otherServiceInfos"] = air_pnr.get("otherServiceInfos", [])

            logger.info(f"[IN_TRIP] Successfully extracted event details: {list(extracted_data.keys())}")
            return extracted_data

        except Exception as e:
            logger.error(f"[IN_TRIP] Error extracting event details: {str(e)}")
            return {}

    @staticmethod
    async def is_in_trip_booking(existing_booking: Booking) -> bool:
        today = datetime.now(timezone.utc)

        if existing_booking.type == "flight":
            flight_booking = InTripAgent._extract_flight_details(existing_booking)
            if flight_booking:
                for flight_leg in flight_booking.legs:
                    if InTripAgent._is_flight_in_trip(flight_leg, today):
                        return True
            return False
        else:
            hotel_booking = InTripAgent._extract_hotel_details(existing_booking)
            if not hotel_booking:
                return False
            return InTripAgent._is_hotel_in_trip(hotel_booking, today)

    @staticmethod
    def _is_flight_in_trip(flight_leg: InTripFlightInfo, current_time: datetime = datetime.now(timezone.utc)) -> bool:
        try:
            # Parse departure and arrival times from flight_leg
            departure_time = dateutil.parser.isoparse(flight_leg.departure_time)
            arrival_time = dateutil.parser.isoparse(flight_leg.arrival_time)

            departure_tz = ZoneInfo(flight_leg.departure_timezone) if flight_leg.departure_timezone else timezone.utc
            departure_time = departure_time.replace(tzinfo=departure_tz)

            arrival_tz = ZoneInfo(flight_leg.arrival_timezone) if flight_leg.arrival_timezone else timezone.utc
            arrival_time = arrival_time.replace(tzinfo=arrival_tz)

            # Direct comparison with timezone-aware datetimes
            departure_minus_72h = departure_time - timedelta(hours=72)
            is_in_trip = departure_minus_72h <= current_time <= arrival_time
            return is_in_trip

        except Exception as e:
            logger.error(f"Error checking flight in-trip status for flight leg {flight_leg.leg_index}: {str(e)}")
            return False

    @staticmethod
    def _is_hotel_in_trip(hotel: InTripHotelBooking, current_time: datetime = datetime.now()) -> bool:
        try:
            start_datetime = hotel.get_check_in_datetime()
            end_datetime = hotel.get_check_out_datetime()
            if start_datetime is None or end_datetime is None:
                return False
            current_time = current_time.replace(tzinfo=None)
            is_in_trip = start_datetime - timedelta(hours=72) <= current_time <= end_datetime
            return is_in_trip

        except Exception as e:
            logger.error(f"Error checking hotel in-trip status for booking {hotel.id}: {str(e)}")
            return False

    @staticmethod
    def _extract_flight_details(booking: Booking) -> InTripFlightBooking | None:
        try:
            content = booking.content
            all_legs_info = []
            if "legs" in content:
                legs = content.get("legs", [])
                for leg_index, leg in enumerate(legs):
                    flight_segments = leg.get("flight_segments", [])
                    for segment in flight_segments:
                        flight_stops = segment.get("flight_stops", [])
                        if flight_stops:
                            first_stop = flight_stops[0]
                            last_stop = flight_stops[-1]

                            departure_time = first_stop.get("departure", "")

                            leg_info = InTripFlightInfo(
                                leg_index=leg_index,
                                confirmation_number=content.get("confirmation_id", ""),
                                boarding_time=None,
                                gate=None,
                                terminal=None,
                                departure_time=departure_time,
                                departure_timezone=first_stop.get("departure_timezone", ""),
                                arrival_time=last_stop.get("arrival", ""),
                                arrival_timezone=last_stop.get("arrival_timezone", ""),
                                airline=first_stop.get("airline_code", ""),
                                flight_numbers=[first_stop.get("flight_number", "")],
                                origin_name=first_stop.get("origin_name", ""),
                                origin_code=first_stop.get("origin_code", ""),
                                destination_name=last_stop.get("destination_name", ""),
                                destination_code=last_stop.get("destination_code", ""),
                                price_string="",
                                seat=first_stop.get("seat", ""),
                            )
                            all_legs_info.append(leg_info)

            elif content.get("outbound") or content.get("return"):
                if content.get("outbound"):
                    outbound = content.get("outbound", {})
                    flight_segments = outbound.get("flight_segments", [])
                    for segment in flight_segments:
                        flight_stops = segment.get("flight_stops", [])
                        if flight_stops:
                            first_stop = flight_stops[0]
                            last_stop = flight_stops[-1]

                            departure_time = first_stop.get("departure_time", "")

                            leg_info = InTripFlightInfo(
                                leg_index=0,
                                confirmation_number=first_stop.get("confirmation", ""),
                                boarding_time=None,
                                gate=None,
                                terminal=None,
                                departure_time=departure_time,
                                departure_timezone=first_stop.get("departure_timezone", ""),
                                arrival_time=last_stop.get("arrival_time", ""),
                                arrival_timezone=last_stop.get("arrival_timezone", ""),
                                airline=first_stop.get("airline_code", ""),
                                flight_numbers=[first_stop.get("flight_number", "")],
                                origin_name=first_stop.get("origin_name", ""),
                                origin_code=first_stop.get("origin", ""),
                                destination_name=last_stop.get("destination_name", ""),
                                destination_code=last_stop.get("destination", ""),
                                price_string="",
                                seat=first_stop.get("seat_number", ""),
                            )
                            all_legs_info.append(leg_info)

                if content.get("return"):
                    return_flight = content.get("return", {})
                    return_flight_segments = return_flight.get("flight_segments", [])
                    for segment in return_flight_segments:
                        flight_stops = segment.get("flight_stops", [])
                        if flight_stops:
                            first_stop = flight_stops[0]
                            last_stop = flight_stops[-1]

                            departure_time = first_stop.get("departure_time", "")

                            leg_info = InTripFlightInfo(
                                leg_index=1,
                                confirmation_number=first_stop.get("confirmation", ""),
                                departure_time=departure_time,
                                departure_timezone=first_stop.get("departure_timezone", ""),
                                arrival_time=last_stop.get("arrival_time", ""),
                                arrival_timezone=last_stop.get("arrival_timezone", ""),
                                airline=first_stop.get("airline_code", ""),
                                flight_numbers=[first_stop.get("flight_number", "")],
                                origin_name=first_stop.get("origin_name", ""),
                                origin_code=first_stop.get("origin", ""),
                                destination_name=last_stop.get("destination_name", ""),
                                destination_code=last_stop.get("destination", ""),
                                price_string="",
                                seat=first_stop.get("seat_number", ""),
                            )
                            all_legs_info.append(leg_info)
            if all_legs_info:
                return InTripFlightBooking(legs=all_legs_info)
            else:
                return None

        except Exception as e:
            logger.error(f"Error parsing flight legs: {str(e)}")
            return None

    @staticmethod
    async def parse_flight_legs_from_trip_details(trip_id: str) -> list[InTripFlightInfo]:
        """Parse all flight legs from trip details API and return as list.

        Calls get trip details API and extracts flight leg data.
        Extracts: departure time, arrival time, confirmation number, seat.

        Args:
            trip_id: Trip ID to fetch details for

        Returns:
            List of flight leg dictionaries with extracted data
        """
        try:
            from flight_agent.flights_tools import FlightSearchTools

            trip_details = await FlightSearchTools.get_trip_details_spotnana(trip_id)

            if not trip_details or not trip_details.get("pnrs"):
                logger.error(f"No trip details found for trip_id {trip_id}")
                return []

            pnr_data = trip_details.get("pnrs", [{}])[0].get("data", {})
            air_pnr = pnr_data.get("airPnr", {})
            legs = air_pnr.get("legs", [])

            traveler_infos = air_pnr.get("travelerInfos", [])
            seats_map = {}
            if traveler_infos:
                seats = traveler_infos[0].get("booking", {}).get("seats", [])
                for seat in seats:
                    leg_idx = seat.get("legIdx", 0)
                    flight_idx = seat.get("flightIdx", 0)
                    seat_number = seat.get("number", "")
                    seats_map[(leg_idx, flight_idx)] = seat_number

            all_legs_info = []

            for leg_index, leg in enumerate(legs):
                flights = leg.get("flights", [])
                if flights:
                    first_flight = flights[0]
                    last_flight = flights[-1]

                    departure_time = first_flight.get("departureDateTime", {}).get("iso8601", "")

                    seat_number = seats_map.get((leg_index, 0), "")

                    marketing = first_flight.get("marketing", {})
                    airline_code = marketing.get("airlineCode", "")
                    flight_number = marketing.get("num", "")

                    leg_info = InTripFlightInfo(
                        leg_index=leg_index + 1,
                        confirmation_number=first_flight.get("vendorConfirmationNumber", ""),
                        departure_time=departure_time,
                        departure_timezone="",
                        arrival_time=last_flight.get("arrivalDateTime", {}).get("iso8601", ""),
                        arrival_timezone="",
                        airline=airline_code,
                        flight_numbers=[flight_number],
                        origin_name="",
                        origin_code=first_flight.get("origin", ""),
                        destination_name="",
                        destination_code=last_flight.get("destination", ""),
                        price_string="",
                        seat=seat_number,
                    )
                    all_legs_info.append(leg_info)

            return all_legs_info

        except Exception as e:
            logger.error(f"Error parsing flight legs from trip details for trip_id {trip_id}: {str(e)}")
            return []

    @staticmethod
    def _format_flight_message(
        flight_info: InTripFlightInfo, current_time: datetime = datetime.now(timezone.utc)
    ) -> str:
        """Generate formatted in-trip message for flight notifications."""
        try:
            airline_code = flight_info.airline
            flight_number = flight_info.flight_numbers[0] if flight_info.flight_numbers else ""
            flight_id = f"{airline_code} {flight_number}" if airline_code and flight_number else "Flight"

            route = (
                f"{flight_info.origin_code} → {flight_info.destination_code}"
                if flight_info.origin_code and flight_info.destination_code
                else ""
            )

            departure_time = dateutil.parser.isoparse(flight_info.departure_time)
            departure_tz = ZoneInfo(flight_info.departure_timezone) if flight_info.departure_timezone else timezone.utc
            departure_time = departure_time.replace(tzinfo=departure_tz)
            departure_str = departure_time.strftime("%b %d at %H:%M")

            departure_minutes_left = (departure_time - current_time).total_seconds() / 60
            status = "On time" if departure_minutes_left > 30 else "Delayed"

            # Calculate days difference for the greeting message
            current_date = current_time.date()
            departure_date = departure_time.date()
            days_difference = (departure_date - current_date).days

            if days_difference == 0:
                greeting = (
                    "Hello! I see you're currently on this trip, and your next flight is scheduled to depart today:"
                )
            else:
                greeting = f"Hello! I see you have an upcoming trip, with this flight scheduled to depart in {days_difference} days: "

            message_parts = [
                greeting,
                "",
                "---",
                "",
                f"✈️ **{route}** ({flight_id})",
                f"• **Status**: {status}",
            ]

            if departure_time > current_time:
                hours = int(departure_minutes_left // 60)
                minutes = int(departure_minutes_left % 60)
                if hours > 0:
                    message_parts.append(f"• **Departure**: {departure_str} (in {hours} hrs {minutes} mins)")
                else:
                    message_parts.append(f"• **Departure**: {departure_str} (in {minutes} mins)")
            else:
                message_parts.append(f"• **Departure**: {departure_str}")

            try:
                arrival_time = dateutil.parser.isoparse(flight_info.arrival_time)
                arrival_str = arrival_time.strftime("%b %d at %H:%M")
                message_parts.append(f"• **Arrival** : {arrival_str}")
            except Exception:
                pass

            if flight_info.seat:
                message_parts.append(f"• **Seat**: {flight_info.seat}")

            if flight_info.terminal:
                message_parts.append(f"• **Terminal**: Terminal {flight_info.terminal}")

            if flight_info.gate:
                message_parts.append(f"• **Gate** : {flight_info.gate}")

            if flight_info.confirmation_number:
                message_parts.append(f"• **Confirmation Number**: {flight_info.confirmation_number}")

            message_parts.extend(["", "---", "", "Can I help you with something?"])

            return "\n".join(message_parts)

        except Exception as e:
            logger.error(f"Error generating flight in-trip message: {str(e)}")
            return "Hello! I see you're currently on this trip. Can I help you with something?"

    @staticmethod
    def _format_flight_message_enhanced(
        flight_booking: InTripFlightBooking, current_time: datetime = datetime.now(timezone.utc)
    ) -> str:
        """Generate enhanced formatted in-trip message for flight notifications with update tracking."""
        try:
            if not flight_booking.legs:
                return "Hello! I see you're currently on this trip. Can I help you with something?"

            # Use first leg for basic flight info
            first_leg = flight_booking.legs[0]
            airline_code = first_leg.airline
            flight_number = first_leg.flight_numbers[0] if first_leg.flight_numbers else ""
            flight_id = f"{airline_code} {flight_number}" if airline_code and flight_number else "Flight"

            route = (
                f"{flight_booking.origin_code} → {flight_booking.destination_code}"
                if flight_booking.origin_code and flight_booking.destination_code
                else ""
            )

            # Use departure time (already reflects current status)
            departure_time_str = flight_booking.departure_time
            departure_time = dateutil.parser.isoparse(departure_time_str)
            departure_tz = (
                ZoneInfo(flight_booking.departure_timezone) if flight_booking.departure_timezone else timezone.utc
            )
            departure_time = departure_time.replace(tzinfo=departure_tz)
            departure_str = departure_time.strftime("%b %d at %H:%M")

            # Use effective status
            status = flight_booking.get_effective_status()

            # Calculate days difference for the greeting message
            current_date = current_time.date()
            departure_date = departure_time.date()
            days_difference = (departure_date - current_date).days

            if days_difference == 0:
                greeting = (
                    "Hello! I see you're currently on this trip, and your next flight is scheduled to depart today:"
                )
            else:
                greeting = f"Hello! I see you have an upcoming trip, with this flight scheduled to depart in {days_difference} days: "

            message_parts = [
                greeting,
                "",
                "---",
                "",
                f"✈️ **{route}** ({flight_id})",
                f"• **Status**: {status}",
            ]

            # Departure time with countdown
            if departure_time > current_time:
                departure_minutes_left = (departure_time - current_time).total_seconds() / 60
                hours = int(departure_minutes_left // 60)
                minutes = int(departure_minutes_left % 60)
                if hours > 0:
                    message_parts.append(f"• **Departure**: {departure_str} (in {hours} hrs {minutes} mins)")
                else:
                    message_parts.append(f"• **Departure**: {departure_str} (in {minutes} mins)")
            else:
                message_parts.append(f"• **Departure**: {departure_str}")

            # Arrival time (already reflects current status)
            try:
                arrival_time_str = flight_booking.arrival_time
                arrival_time = dateutil.parser.isoparse(arrival_time_str)
                arrival_str = arrival_time.strftime("%b %d at %H:%M")
                message_parts.append(f"• **Arrival**: {arrival_str}")
            except Exception:
                pass

            # Gate and terminal info
            if gate := flight_booking.get_effective_gate():
                message_parts.append(f"• **Gate**: {gate}")

            if terminal := flight_booking.get_effective_terminal():
                message_parts.append(f"• **Terminal**: Terminal {terminal}")

            if boarding_time := flight_booking.get_effective_boarding_time():
                message_parts.append(f"• **Boarding**: {boarding_time}")

            # Seat info from first leg
            if first_leg.seat:
                message_parts.append(f"• **Seat**: {first_leg.seat}")

            # Confirmation number
            if first_leg.confirmation_number:
                message_parts.append(f"• **Confirmation Number**: {first_leg.confirmation_number}")

            # Show recent updates if any
            trip_updates = flight_booking.get_trip_update_summary()
            recent_combined = flight_booking.get_recent_updates_combined(2)  # Last 2 hours

            if trip_updates or recent_combined:
                message_parts.append("")
                message_parts.append("📋 **Recent Updates:**")

                # Show trip-wide updates first
                if trip_updates:
                    message_parts.append(f"• 🚨 **Trip**: {trip_updates}")

                # Show leg-specific updates
                for leg in flight_booking.legs:
                    if leg_changes := leg.get_change_summary():
                        route_info = (
                            f"{leg.origin_code}→{leg.destination_code}"
                            if leg.origin_code and leg.destination_code
                            else f"Leg {leg.leg_index}"
                        )
                        message_parts.append(f"• ✈️ **{route_info}**: {leg_changes}")

            message_parts.extend(["", "---", "", "Can I help you with something?"])

            return "\n".join(message_parts)

        except Exception as e:
            logger.error(f"Error generating enhanced flight in-trip message: {str(e)}")
            return "Hello! I see you're currently on this trip. Can I help you with something?"

    @staticmethod
    def _extract_hotel_details(booking: Booking) -> InTripHotelBooking | None:
        """Extract hotel details from booking.content into InTripHotelInfo object."""
        try:
            content = booking.content
            hotel_name = content.get("hotel", "")
            if not hotel_name:
                return None

            return InTripHotelBooking(
                id=booking.id,
                hotel_name=hotel_name,
                check_in_date=content.get("check_in_date", ""),
                check_in_time=content.get("check_in_time", ""),
                check_out_date=content.get("check_out_date", ""),
                check_out_time=content.get("check_out_time", ""),
                room_type=content.get("room", {}).get("option_title", ""),
                address=content.get("mapMarker", {}).get("address", ""),
                confirmation_number=content.get("reservation_number", ""),
                status=content.get("status"),
            )

        except Exception as e:
            logger.error(f"Error extracting hotel details for booking {booking.id}: {str(e)}")
            return None

    @staticmethod
    def _format_hotel_message(
        hotel_info: InTripHotelBooking, current_time: datetime = datetime.now(timezone.utc)
    ) -> str:
        """Format hotel info object into the message format shown in screenshot."""
        try:
            # Calculate days difference for the greeting message
            check_in_datetime = hotel_info.get_check_in_datetime()
            if check_in_datetime:
                current_date = current_time.date()
                check_in_date = check_in_datetime.date()
                days_difference = (check_in_date - current_date).days

                if days_difference == 0:
                    greeting = "Hello! I see you're on your trip and currently staying at:"
                else:
                    greeting = "Hello! I see you have an upcoming trip, and your stay is booked at:"
            else:
                # Fallback if we can't parse check-in datetime
                greeting = "Hello! I see you're on your trip and currently staying at:"

            message_parts = [greeting]
            message_parts.extend(["", "---", "", f"🏨 **{hotel_info.hotel_name}**"])

            # Use the new methods to get datetime objects
            check_in_datetime = hotel_info.get_check_in_datetime()
            if check_in_datetime:
                check_in_str = check_in_datetime.strftime("%b %d at %H:%M")
                message_parts.append(f"• **Check-in**: {check_in_str}")
            elif hotel_info.check_in_date:
                # Fallback to raw date/time if parsing failed
                if hotel_info.check_in_time:
                    message_parts.append(f"• **Check-in**: {hotel_info.check_in_date} at {hotel_info.check_in_time}")
                else:
                    message_parts.append(f"• **Check-in**: {hotel_info.check_in_date}")

            check_out_datetime = hotel_info.get_check_out_datetime()
            if check_out_datetime:
                check_out_str = check_out_datetime.strftime("%b %d at %H:%M")
                message_parts.append(f"• **Check-out**: {check_out_str}")
            elif hotel_info.check_out_date:
                # Fallback to raw date/time if parsing failed
                if hotel_info.check_out_time:
                    message_parts.append(f"• **Check-out**: {hotel_info.check_out_date} at {hotel_info.check_out_time}")
                else:
                    message_parts.append(f"• **Check-out**: {hotel_info.check_out_date}")

            if hotel_info.room_type:
                message_parts.append(f"• **Room**: {hotel_info.room_type}")

            if hotel_info.address:
                message_parts.append(f"• **Address**: {hotel_info.address}")

            if hotel_info.confirmation_number:
                message_parts.append(f"• **Confirmation Number**: {hotel_info.confirmation_number}")

            message_parts.extend(["", "---", "", "Can I help you with something?"])

            return "\n".join(message_parts)

        except Exception as e:
            logger.error(f"Error formatting hotel message: {str(e)}")
            return "Hello! I see you're currently on this trip. Can I help you with something?"

    @staticmethod
    def _format_hotel_message_enhanced(
        hotel_info: InTripHotelBooking, current_time: datetime = datetime.now(timezone.utc)
    ) -> str:
        """Enhanced format hotel info object with update tracking and current status."""
        try:
            # Calculate days difference for the greeting message
            check_in_datetime = hotel_info.get_check_in_datetime()
            if check_in_datetime:
                current_date = current_time.date()
                check_in_date = check_in_datetime.date()
                days_difference = (check_in_date - current_date).days

                if days_difference == 0:
                    greeting = "Hello! I see you're on your trip and currently staying at:"
                else:
                    greeting = "Hello! I see you have an upcoming trip, and your stay is booked at:"
            else:
                # Fallback if we can't parse check-in datetime
                greeting = "Hello! I see you're on your trip and currently staying at:"

            message_parts = [greeting]
            message_parts.extend(["", "---", "", f"🏨 **{hotel_info.hotel_name}**"])

            # Show status (already reflects current status)
            if hotel_info.status and hotel_info.status != "confirmed":
                message_parts.append(f"• **Status**: {hotel_info.status.title()}")

            # Use check-in datetime (already reflects current status)
            check_in_datetime = hotel_info.get_check_in_datetime()
            if check_in_datetime:
                check_in_str = check_in_datetime.strftime("%b %d at %H:%M")
                message_parts.append(f"• **Check-in**: {check_in_str}")
            elif hotel_info.check_in_date:
                # Fallback to raw date/time if parsing failed
                if hotel_info.check_in_time:
                    message_parts.append(f"• **Check-in**: {hotel_info.check_in_date} at {hotel_info.check_in_time}")
                else:
                    message_parts.append(f"• **Check-in**: {hotel_info.check_in_date}")

            # Use check-out datetime (already reflects current status)
            check_out_datetime = hotel_info.get_check_out_datetime()
            if check_out_datetime:
                check_out_str = check_out_datetime.strftime("%b %d at %H:%M")
                message_parts.append(f"• **Check-out**: {check_out_str}")
            elif hotel_info.check_out_date:
                # Fallback to raw date/time if parsing failed
                if hotel_info.check_out_time:
                    message_parts.append(f"• **Check-out**: {hotel_info.check_out_date} at {hotel_info.check_out_time}")
                else:
                    message_parts.append(f"• **Check-out**: {hotel_info.check_out_date}")

            # Use room type (already reflects current status)
            if hotel_info.room_type:
                message_parts.append(f"• **Room**: {hotel_info.room_type}")

            if hotel_info.address:
                message_parts.append(f"• **Address**: {hotel_info.address}")

            if hotel_info.confirmation_number:
                message_parts.append(f"• **Confirmation Number**: {hotel_info.confirmation_number}")

            # Show recent updates if any
            if recent_changes := hotel_info.get_change_summary():
                message_parts.extend(["", "📋 **Recent Updates:**", f"• {recent_changes}"])

            message_parts.extend(["", "---", "", "Can I help you with something?"])

            return "\n".join(message_parts)

        except Exception as e:
            logger.error(f"Error formatting enhanced hotel message: {str(e)}")
            return "Hello! I see you're currently on this trip. Can I help you with something?"

    @staticmethod
    async def get_in_trip_status_and_message(
        existing_booking: Booking, current_tz: str | None = None
    ) -> tuple[bool, str | None]:
        """Get both in-trip status and formatted message for frontend."""
        current_time = datetime.now(timezone.utc)

        # Collect all bookings with their start times for sorting
        all_bookings = []

        if existing_booking.type == "flight":
            flight_booking = InTripAgent._extract_flight_details(existing_booking)
            if flight_booking and flight_booking.legs:
                for leg in flight_booking.legs:
                    departure_dt = leg.get_departure_datetime()
                    if departure_dt:
                        all_bookings.append(
                            {
                                "type": "flight",
                                "start_time": departure_dt,
                                "data": leg,
                                "is_in_trip": InTripAgent._is_flight_in_trip(leg, current_time),
                            }
                        )
        elif existing_booking.type == "accommodations":
            hotel_info = InTripAgent._extract_hotel_details(existing_booking)
            if hotel_info:
                check_in_dt = hotel_info.get_check_in_datetime()
                if check_in_dt:
                    all_bookings.append(
                        {
                            "type": "hotel",
                            "start_time": check_in_dt,
                            "data": hotel_info,
                            "is_in_trip": InTripAgent._is_hotel_in_trip(hotel_info, current_time),
                        }
                    )

        if not all_bookings:
            return False, None

        # Sort all bookings by start time
        all_bookings.sort(key=lambda x: x["start_time"])

        # Check if any booking is currently in trip
        for booking in all_bookings:
            if booking["is_in_trip"]:
                if booking["type"] == "flight":
                    message = InTripAgent._format_flight_message(booking["data"], current_time)
                else:  # hotel
                    message = InTripAgent._format_hotel_message(booking["data"], current_time)
                return True, message

        # If no booking is currently in trip, find the nearest upcoming booking
        current_time_naive = current_time.replace(tzinfo=None)
        upcoming_bookings = [
            booking for booking in all_bookings if booking["start_time"].replace(tzinfo=None) > current_time_naive
        ]

        if upcoming_bookings:
            nearest_booking = upcoming_bookings[0]  # Already sorted by start time
            if nearest_booking["type"] == "flight":
                message = InTripAgent._format_flight_message(nearest_booking["data"], current_time)
            else:  # hotel
                message = InTripAgent._format_hotel_message(nearest_booking["data"], current_time)
            return False, message

        return False, None

    async def run(self, message=None, message_type="text", extra_payload=None):
        raise NotImplementedError
