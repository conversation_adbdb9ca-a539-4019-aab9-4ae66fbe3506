# Flight & Spotnana Client Migration Plan

## Overview

This document outlines the plan to refactor external API integrations by moving them from scattered locations into a centralized `server/api_clients` package. This migration uses a **function-by-function approach** to gradually replace existing Spotnana functionality while maintaining backward compatibility and minimizing risk.

## Current State Analysis

### Spotnana-related Code Locations
- `server/utils/spotnana_api.py` - Core Spotnana API client (`SpotnanaApi`, `SpotnanaHelper`)
- `flight_agent/flights_tools.py` - `FlightSearchTools` class with Spotnana flight operations
- `front_of_house_agent/spotnana_client.py` - `SpotnanaClient` for flight search operations
- `hotel_agent/spotnana_hotels_helper.py` - Hotel-specific Spotnana operations

### Dependencies Identified

**Total Impact Analysis:** 217 function calls across 26 files

**FlightSearchTools** is used by 10+ files:
- `front_of_house_agent/back_of_house_executor/flight_search_functions.py` (3 usages)
- `front_of_house_agent/back_of_house_executor/flight_executor_functions.py` (29 usages)
- `flight_agent/flights_helper.py` (30 usages)
- `tests/test_flights_tools.py` (3 usages)
- `scripts/get_flight_results.py`, `scripts/flight-poc.py`, `scripts/backfill_airline_confirmation_number.py`
- `front_of_house_agent/back_of_house_executor/seat_selection_executor.py` (5 usages)
- `front_of_house_agent/spotnana_client.py` (12 usages)

**SpotnanaApi** is used by 8 files:
- `server/services/user_profile/personal_information.py` (5 usages)
- `server/services/user_profile/payment_information.py` (5 usages)
- `server/services/user/user_account_delete.py` (2 usages)
- `server/services/payment_profile/spotnana_profile.py` (9 usages)
- `hotel_agent/spotnana_hotels_helper.py` (11 usages)
- Plus scripts and tests

## Proposed Structure

The structure is aligned with the [Spotnana OpenAPI organization](https://developer.spotnana.com/) for consistency and future extensibility:

```
server/
├── api_clients/
│   ├── __init__.py
│   ├── spotnana/
│   │   ├── __init__.py
│   │   ├── client.py          # Core authentication & base client
│   │   ├── air.py             # Flight search, booking, seat selection
│   │   ├── hotel.py           # Hotel search and booking operations
│   │   ├── user.py            # User management operations
│   │   ├── trip.py            # Trip management operations
│   │   └── exceptions.py      # Custom exceptions
│   ├── booking_com/
│   │   ├── __init__.py
│   │   └── client.py          # Booking.com API client
│   ├── google/
│   │   ├── __init__.py
│   │   ├── serp_client.py     # Google SERP API
│   │   ├── calendar_client.py # Google Calendar API
│   │   └── maps_client.py     # Google Maps API
│   └── microsoft/
│       ├── __init__.py
│       └── calendar_client.py # Microsoft Calendar API
├── utils/
├── services/
└── ... (existing structure)
```

### Spotnana OpenAPI Structure Reference

The client structure follows the official Spotnana API organization:

- **Air API** ([docs](https://developer.spotnana.com/openapi/airapi)) - Flight search, booking, seat selection
- **Hotel API** ([docs](https://developer.spotnana.com/openapi/hotelapi)) - Hotel search and booking
- **Company API** ([docs](https://developer.spotnana.com/openapi/companyapi)) - Company management, legal entities, cost centers
- **Policy API** ([docs](https://developer.spotnana.com/openapi/policyapi)) - Travel policies, custom fields
- **Trip API** ([docs](https://developer.spotnana.com/openapi/tripapi)) - Trip management, PNRs, invoicing
- **Event API** ([docs](https://developer.spotnana.com/openapi/eventapi)) - Event management and templates
- **Users API** ([docs](https://developer.spotnana.com/openapi/usersapi)) - User management, profiles, travel preferences, roles

This structure provides a clear foundation for implementing all Spotnana APIs while maintaining consistency with their official organization.

## Migration Strategy: Function-by-Function Approach

### Functional Module Analysis & Migration Priority

#### 1. Core Authentication (Blast Radius: 0 files)
**Functions to migrate:**
- `SpotnanaApi.authorize()` → `SpotnanaClient.authenticate()`
- `SpotnanaApi.is_token_expired()` → `SpotnanaClient._is_token_expired()`

**Blast Radius:** 0 direct callers (internal methods only)
**Effort:** Low | **Risk:** Minimal

#### 2. Trip Management (Blast Radius: 4 files)
**Functions to migrate:**
- `SpotnanaApi.get_trip_details()` → `SpotnanaTripsClient.get_details()`
- `SpotnanaApi.create_trip()` → `SpotnanaTripsClient.create()`
- `FlightSearchTools.create_trip_spotnana()` → `SpotnanaTripsClient.create_flight_trip()`
- `FlightSearchTools.get_trip_details_spotnana()` → Use `SpotnanaTripsClient.get_details()`

**Current callers (4 files):**
- `front_of_house_agent/spotnana_client.py` (12 usages)
- `front_of_house_agent/back_of_house_executor/trip_status_executor.py` (1 usage)
- `server/services/trips/spotnana_itinerary.py` (1 usage)
- `flight_agent/flights_tools.py` (2 usages)

**Effort:** Low-Medium | **Risk:** Low

#### 3. Payment Operations (Blast Radius: 4 files)
**Functions to migrate:**
- `SpotnanaApi.create_credit_card()` → `SpotnanaUserClient.create_payment_method()`
- `SpotnanaApi.create_payment_source()` → `SpotnanaUserClient.create_payment_source()`
- `SpotnanaApi.confirm_credit_card()` → `SpotnanaUserClient.confirm_payment_method()`

**Current callers (4 files):**
- `server/services/user_profile/payment_information.py` (5 usages)
- `server/services/payment_profile/spotnana_profile.py` (9 usages)
- `scripts/spotnana_update_credit_card.py` (2 usages)
- `flight_agent/flights_tools.py` (usage for credits)

**Effort:** Medium | **Risk:** Low-Medium

#### 4. User Management Operations (Blast Radius: 8 files)
**Functions to migrate:**
- `SpotnanaApi.get_user_by_email()` → `SpotnanaUserClient.get_by_email()`
- `SpotnanaApi.create_user()` → `SpotnanaUserClient.create()`
- `SpotnanaApi.get_traveler_by_email()` → `SpotnanaUserClient.get_traveler_by_email()`
- `SpotnanaApi.traveler_read()` → `SpotnanaUserClient.get_traveler_details()`

**Current callers (8 files):**
- `server/services/user_profile/personal_information.py` (5 usages)
- `server/services/user_profile/payment_information.py` (5 usages)
- `server/services/user/user_account_delete.py` (2 usages)
- `server/services/payment_profile/spotnana_profile.py` (9 usages)
- `front_of_house_agent/sub_agent/profile_sub_agent.py`
- `hotel_agent/spotnana_hotels_helper.py` (11 usages)
- `flight_agent/flights_tools.py` (2 usages)
- `tests/services/user/test_user_account_delete.py` (6 usages)

**Effort:** Medium | **Risk:** Medium

#### 5. Hotel Operations (Blast Radius: 2 files)
**Functions to migrate:**
- `SpotnanaApi.search_hotels()` → `SpotnanaHotelClient.search()`
- `SpotnanaApi.get_hotel_details()` → `SpotnanaHotelClient.get_details()`
- `SpotnanaApi.validate_hotel_price()` → `SpotnanaHotelClient.validate_price()`
- `SpotnanaApi.create_hotel_booking()` → `SpotnanaHotelClient.create_booking()`
- `SpotnanaApi.cancel_hotel_booking()` → `SpotnanaHotelClient.cancel_booking()`

**Current callers (2 files):**
- `hotel_agent/spotnana_hotels_helper.py` (11 usages)
- `server/utils/spotnana_api.py` (self-reference)

**Effort:** Medium | **Risk:** Medium

#### 6. Flight Seat Selection (Blast Radius: 3 files)
**Functions to migrate:**
- `FlightSearchTools.seat_map_spotnana()` → `SpotnanaAirClient.get_seat_map()`
- `FlightSearchTools.get_seat_map_with_loyalty_comparison()` → `SpotnanaAirClient.get_seat_map_with_loyalty()`

**Current callers (3 files):**
- `front_of_house_agent/back_of_house_executor/seat_selection_executor.py` (5 usages)
- `flight_agent/flights_tools.py` (self-references)
- `flight_agent/flights_helper.py` (usage)

**Effort:** Medium | **Risk:** Medium

#### 7. Flight Booking Operations (Blast Radius: 6 files)
**Functions to migrate:**
- `FlightSearchTools.flight_checkout_spotnana()` → `SpotnanaAirClient.checkout_flight()`
- `FlightSearchTools.initiate_booking_spotnana()` → `SpotnanaAirClient.initiate_booking()`
- `FlightSearchTools.create_pnr_spotnana()` → `SpotnanaAirClient.create_pnr()`
- `FlightSearchTools.flight_validation_spotnana_simplified()` → `SpotnanaAirClient.validate_flight()`

**Current callers (6 files):**
- `front_of_house_agent/back_of_house_executor/flight_executor_functions.py` (heavy usage)
- `flight_agent/flights_helper.py` (multiple usages)
- `flight_agent/flights_tools.py` (self-references)
- `scripts/backfill_airline_confirmation_number.py` (2 usages)
- Plus 2 more files

**Effort:** High | **Risk:** High

#### 8. Flight Search Operations (Blast Radius: 12 files)
**Functions to migrate:**
- `FlightSearchTools.search_flights_spotnana()` → `SpotnanaAirClient.search_flights()`
- `SpotnanaClient.search_flights_spotnana()` → Use `SpotnanaAirClient.search_flights()`
- `FlightSearchTools.get_selected_itinerary_spotnana()` → `SpotnanaAirClient.get_itinerary_details()`
- `SpotnanaClient.get_fare_basis_codes()` → `SpotnanaAirClient.get_fare_basis_codes()`

**Current callers (12 files):**
- `front_of_house_agent/back_of_house_executor/flight_search_functions.py` (3 usages)
- `front_of_house_agent/back_of_house_executor/flight_executor_functions.py` (29 usages)
- `flight_agent/flights_helper.py` (30 usages)
- `front_of_house_agent/spotnana_client.py` (12 usages)
- `tests/test_flights_tools.py` (3 usages)
- `scripts/get_flight_results.py`, `scripts/flight-poc.py`
- Plus 5 more files with lower usage

**Effort:** High | **Risk:** High

## Implementation Strategy

### Phase 1: Foundation (Week 1)
1. Create `server/api_clients/spotnana/` structure
2. Implement core authentication in `client.py`
3. Import existing models from `models/api/spotnana/` and create exceptions
4. Set up comprehensive testing framework

### Phase 2: Low-Risk Migrations (Week 2)
1. **Core Authentication** - Migrate and test internally
2. **Trip Management** - 4 files, clean interfaces
3. **Payment Operations** - 4 files, well-isolated

### Phase 3: Medium-Risk Migrations (Week 3-4)
1. **User Management** - 8 files, service layer focused
2. **Hotel Operations** - 2 files, agent-specific
3. **Flight Seat Selection** - 3 files, specific feature

### Phase 4: High-Risk Migrations (Week 5-6)
1. **Flight Booking** - 6 files, critical business logic
2. **Flight Search** - 12 files, core functionality

## Function-by-Function Implementation Pattern

### For Each Function Migration:

1. **Implement New Function** in appropriate client module
2. **Create Backward-Compatible Wrapper** in original location
3. **Add Feature Flag** to switch between old/new implementations
4. **Migrate One Caller at a Time** with thorough testing
5. **Remove Wrapper** once all callers migrated

### Example Migration Pattern:

```python
# New implementation in server/api_clients/spotnana/user.py
from models.api.spotnana.users.models import UserProfile, UserCreateRequest

class SpotnanaUserClient:
    async def get_by_email(self, email: str) -> UserProfile:
        # Clean new implementation with proper typing using models from models/api/spotnana/
        pass

# Backward-compatible wrapper in server/utils/spotnana_api.py
class SpotnanaApi:
    async def get_user_by_email(self, user_email: str):
        # Feature flag approach
        if settings.USE_NEW_SPOTNANA_CLIENT:
            from server.api_clients.spotnana import SpotnanaUserClient
            client = SpotnanaUserClient()
            return await client.get_by_email(user_email)
        else:
            # Original implementation
            return await self._original_get_user_by_email(user_email)
```

### Migration Workflow Per Function:

1. **Analyze Function** - Understand current behavior, dependencies, and callers
2. **Design Clean Interface** - Create improved API with proper typing and error handling
3. **Implement & Test** - Build new function with comprehensive test coverage
4. **Add Feature Flag** - Enable gradual rollout with easy rollback
5. **Migrate Callers** - Update one caller at a time with testing
6. **Monitor & Validate** - Ensure identical behavior in production
7. **Remove Legacy** - Clean up old implementation once fully migrated

## Benefits of This Approach

1. **Gradual Risk Management** - Small incremental changes minimize blast radius
2. **Isolated Testing** - Each function can be tested independently
3. **Feature Flags** - Easy rollback if issues arise
4. **Clean Architecture** - New code follows better patterns and typing
5. **Backward Compatibility** - No breaking changes during migration
6. **Measurable Progress** - Clear completion criteria per function
7. **Production Safety** - Can validate each function in production before full migration

## Testing Strategy

1. **Unit Testing**: Comprehensive test coverage for each new client function
2. **Integration Testing**: End-to-end testing of migrated functionality
3. **Feature Flag Testing**: Validation that both old and new implementations work identically
4. **Performance Testing**: Ensure new implementations meet performance requirements
5. **Rollback Testing**: Verify feature flags can safely revert to legacy implementations

## Success Metrics

- **Zero Downtime** during migration
- **100% Test Coverage** for new implementations
- **Performance Parity** or improvement vs. existing code
- **Clean Separation** of concerns in new client structure
- **Documentation** for all new client interfaces
- **Monitoring** for successful function-by-function rollouts

## Rollback Plan

### Per-Function Rollback:
1. **Feature Flag Revert** - Instantly switch back to legacy implementation
2. **Caller Rollback** - Revert individual file imports without affecting others
3. **Monitoring Alerts** - Automated detection of performance or error regressions

### Full Migration Rollback:
1. Keep original files until migration is complete
2. All backward-compatible wrappers provide immediate fallback
3. Full rollback possible by removing `server/api_clients/` package

## Future Considerations

This migration sets the foundation for:

- **Spotnana API Expansion**: Structure aligned with [Spotnana OpenAPI](https://developer.spotnana.com/) supports future implementation of:
  - Company API (`server/api_clients/spotnana/company.py`) - **Models available in `models/api/spotnana/company/`**
  - Policy API (`server/api_clients/spotnana/policy.py`) - **Models available in `models/api/spotnana/policy/`**
  - Event API (`server/api_clients/spotnana/event.py`) - **Models available in `models/api/spotnana/event/`**
  - Users API (`server/api_clients/spotnana/users.py`) - **Models available in `models/api/spotnana/users/`**
  - Air API (`server/api_clients/spotnana/air.py`) - **Models available in `models/api/spotnana/air/`**
  - Hotel API (`server/api_clients/spotnana/hotel.py`) - **Models available in `models/api/spotnana/hotel/`**
  - Trip API (`server/api_clients/spotnana/trip.py`) - **Models available in `models/api/spotnana/trip/`**
- Moving Booking.com client code to `server/api_clients/booking_com/`
- Moving Google API clients to `server/api_clients/google/`
- Moving Microsoft API clients to `server/api_clients/microsoft/`
- Better integration with the DTO architecture in `models/`
- Centralized external API configuration and monitoring

### Model Migration Strategy

**Excellent News**: We now have comprehensive external API models available in `models/api/spotnana/` generated from the official OpenAPI specifications! This eliminates the need for temporary model files in the client code.

#### Current Model Availability
All Spotnana API models are now available in the proper `models/` package structure:

- **Air API**: `models/api/spotnana/air/models.py` - Flight search, booking, seat selection models
- **Hotel API**: `models/api/spotnana/hotel/models.py` - Hotel search and booking models
- **Company API**: `models/api/spotnana/company/models.py` - Company management models
- **Policy API**: `models/api/spotnana/policy/models.py` - Travel policy models
- **Trip API**: `models/api/spotnana/trip/models.py` - Trip management models
- **Event API**: `models/api/spotnana/event/models.py` - Event management models
- **Users API**: `models/api/spotnana/users/models.py` - User management models

#### Client Implementation Strategy
The new API clients in `server/api_clients/spotnana/` should:

1. **Import Models Directly** from `models/api/spotnana/{domain}/models.py`
2. **Use Proper Typing** with these generated Pydantic v2 models
3. **No Local Models** - All models come from the centralized `models/` package
4. **Leverage Generated Types** for request/response validation and serialization

#### Example Usage Pattern
```python
# server/api_clients/spotnana/air.py
from models.api.spotnana.air.models import (
    AirSearchRequest,
    AirSearchResponse,
    FlightBookingRequest,
    FlightBookingResponse,
    SeatMapRequest,
    SeatMapResponse
)

class SpotnanaAirClient:
    async def search_flights(self, request: AirSearchRequest) -> AirSearchResponse:
        # Implementation using proper typing
        pass
        
    async def book_flight(self, request: FlightBookingRequest) -> FlightBookingResponse:
        # Implementation using proper typing
        pass
```

#### Migration Benefits
This approach provides:

1. **Immediate Type Safety** - All models are properly typed with Pydantic v2
2. **API Consistency** - Models match official Spotnana OpenAPI specifications exactly
3. **Automatic Updates** - Models can be regenerated when Spotnana updates their APIs
4. **Clean Architecture** - Client code focuses on business logic, not data models
5. **No Technical Debt** - Skip temporary models entirely

#### Future DTO Integration
These models in `models/api/spotnana/` represent the **External Layer** of the DTO architecture outlined in `docs/dto_plan.md`. As we continue the DTO migration:

- **External Layer** (`models/api/spotnana/`): ✅ **Already Complete** - Raw API response models from Spotnana
- **Domain Layer** (`models/domain/`): Convert external models to unified business logic models
- **API Layer** (`models/api/`): Transform domain models to frontend-optimized responses

This positions us perfectly for the broader DTO migration while providing immediate value for the client consolidation.