{"permissions": {"allow": ["Bash(uv run pytest:*)", "<PERSON><PERSON>(python:*)", "Bash(ruff check:*)", "Bash(PYTHONPATH=/Users/<USER>/WorkSpace/otto-server uv run pytest tests/test_in_trip_field_changes.py -v)", "Bash(PYTHONPATH=/Users/<USER>/WorkSpace/otto-server uv run pytest tests/test_in_trip_field_changes.py::TestInTripFieldChanges::test_normalize_flight_status_common_codes -v)", "Bash(PYTHONPATH=/Users/<USER>/WorkSpace/otto-server uv run pytest tests/test_in_trip_field_changes.py::TestInTripFieldChanges::test_normalize_flight_status_case_insensitive tests/test_in_trip_field_changes.py::TestInTripFieldChanges::test_normalize_flight_status_keyword_matching tests/test_in_trip_field_changes.py::TestInTripFieldChanges::test_status_normalization_in_flight_leg_changes -v)", "Bash(pyright:*)", "<PERSON><PERSON>(just b:*)", "Bash(ruff format:*)"], "deny": []}}